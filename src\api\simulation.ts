/*
 * @Description: 运控
 * @Author: tzs
 * @Date: 2024-09-11 11:47:19
 * @LastEditors: 老范
 * @LastEditTime: 2025-06-20 11:15:57
 */
import http from '@/utils/http';

// 想定列表
export const getListApi = (params: Object | undefined) => {
  return http.get(`/api/v1/scenario`, params);
};
// 想定信息
export const getScenarioInfoApi = (id: number) => {
  return http.get(`/api/v1/scenario/${id}`);
};
// 想定列表查存档列表
export const getKeepFileById = (params: Object | undefined) => {
  return http.get(`/api/v1/scenario-archive`, params);
};
// 想定校验
export const scenarioVerifyApi = (id: number) => {
  return http.get(`/api/v1/scenario/${id}/verify/result`);
};
// 查询想定推演配置
export const getScenarioConfig = (id: number | string) => {
  return http.get(`/api/v1/scenario/loadConfig/${id}`);
};
// 设置想定推演配置
export const setScenarioConfig = (id: number, params: Object | undefined) => {
  return http.post(`/api/v1/scenario/setConfig/${id}`, params);
};
// 想定设置全局推演配置
export const setGlobalConfig = (params: Object | undefined) => {
  return http.post(`/api/v1/configKey`, params);
};
// 想定获取全局推演配置
export const getGlobalConfig = () => {
  return http.get(`/api/v1/configKey/sys_scenario_config`);
};

// // 添加想定存档列表
// export const addKeepFileList = (params: any) => {
//   return http.delete(`/api/v1/scenario-archive`, params);
// };
// 给想定存档列表添加存档
export const addKeepFileToList = (params: object | undefined) => {
  return http.post(`/api/v1/scenario-archive/record`, params);
};
// 读取跳转至存档
export const turnToKeepFile = (params: {id: number; timeStamp: number; mode: boolean; speedRatio: number; step: number}) => {
  return http.post(`/api/v1/data-server/restore`, params, 'control');
};
// 删除存档列表
export const deleteKeepFileList = (params: {ids: number[]}) => {
  return http.delete(`/api/v1/scenario-archive`, params);
};
// 删除某一存档
export const deleteKeepFileById = (params: {ids: number[]}) => {
  return http.delete(`/api/v1/scenario-archive/record`, params);
};

// 回放列表
export const getPlaybackListApi = (id: number) => {
  return http.get(`/api/v1/scenario-simulation?scenarioId=${id}`);
};
// 回放导出
export const exportPlaybackApi = (tableName: string, type: string) => {
  return `${globalConfig.ControlUrl3D}/api/v1/data-export/${tableName}/simulation?type=${type}`;
};
// 删除回放记录
export const deletePlaybackApi = (params: Object | undefined) => {
  return http.delete(`/api/v1/scenario-simulation`, params);
};
// 回放事件列表
export const getPlaybackEventListApi = (params: Object | undefined) => {
  return http.post(`/api/v1/data-server/queryEvents`, params, 'control');
};
// 回访事件跳转
export const goEventTimeApi = (params: Object | undefined) => {
  return http.post(`/api/v1/data-server/control`, params, 'control');
};
//想定运行初始化, id，mode: 0仿真，1回放
export const scenarioRunInitApi = (params: Object | undefined) => {
  return http.post(`/api/v1/data-server/init`, params, 'control');
};
//控制
export const scenarioControlApi = (params: Object | undefined) => {
  return http.post(`/api/v1/data-server/control`, params, 'control');
};

//socket链接获取数据(init：初始化数据，realtime：仿真数据，state:实体销毁通道)
export const scenarioSocket = (pushType: string) => {
  return `${globalConfig.ControlWSURL3D}/api/v1/data-socket/getModelData?pushType=${pushType}`;
};

//进度socket(获取当前仿真状态)
export const getSimProgressStateSocket = () => {
  return `${globalConfig.ControlWSURL3D}/api/v1/data-socket/getSysInfo`;
};
//进度socket(获取实时仿真进度)
export const getSimRealTimeSocket = () => {
  return `${globalConfig.ControlWSURL3D}/api/v1/data-socket/getSysInfo?type=realtime`;
};
//状态socket(获取仿真状态、配置)
export const getSimStatusConfigSocket = () => {
  return `${globalConfig.ControlWSURL3D}/api/v1/data-socket/getSysInfo?type=init`;
};
