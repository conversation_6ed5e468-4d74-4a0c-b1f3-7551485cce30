<!--
 * @Author: 老范
 * @Date: 2023-10-26 13:50:19
 * @LastEditors: 老范
 * @LastEditTime: 2025-07-08 14:13:43
 * @Description: UnityVue加载unity
-->

<script setup lang="ts">
import {ElLoading, ElMessage} from 'element-plus';
import {RefreshRight} from '@element-plus/icons-vue';
import {ref, reactive, onMounted, watch, nextTick} from 'vue';
import {storeToRefs} from 'pinia';
import UnityWebgl from 'unity-webgl';
import UnityVue from 'unity-webgl/vue';
import debounce from '@/utils/debounce';
import ScenarioList from './simDialog/ScenarioList.vue';
import NodeDesign from '@/components/NodeDesign.vue';
import PlaybackList from './simDialog/PlaybackList.vue';
import PlaybakEventList from './simDialog/PlaybakEventList.vue';
import {useControler} from '@/pinia/controler';
import {useScenarioStore} from '@/pinia/scenario';
import {useConfigState} from '@/pinia/configData';
import {scenarioRunInitApi, scenarioControlApi, getSimRealTimeSocket, getSimStatusConfigSocket, addKeepFileToList, getScenarioConfig, getGlobalConfig, setGlobalConfig, setScenarioConfig, scenarioVerifyApi} from '@/api/simulation';
import timer from '@/utils/timer';
import SpeedConfig from '@/components/SpeedConfig.vue';
const {statusList} = storeToRefs(useControler());
const scenarioStore = useScenarioStore();
const configDataController = useConfigState();

type StateType = {
  isInit: boolean;
  isStart: boolean;
  isPause: boolean;
  isContinue: boolean;
  isStop: boolean;
  isReset: boolean;
  simProgressSocket: null | WebSocket;
  simStatusConfigSocket: null | WebSocket;
  runningMode: number;
  simTime: number;
  jumpTo: number;
  jumpType: string;
  config: {
    archiveId: number;
    mode: boolean;
    replayRecord: boolean;
    automatic: boolean;
    ifNodeDesign: boolean;
    interval: number;
    unit: number;
    step: number;
  };
  onFileDesc: string;
  recordStatus: number;
  currentScenarioId: number;
  dataTableName: string;
  simTimeRange: number;
  taskStatus: number;
  timeStamp: number;
};

const timeScale = ref();
const urlRef = ref(baseConfig.earthToolUrl);
console.log('🚀 ~ baseConfig:', baseConfig.earthToolUrl);
const DEFALUT_SIM_DURATION = 3600 * 1000;
onMounted(async () => {
  initSockect();
  // 获取全局推演配置
  await getGlobalCongigFunc();
  // 设置默认仿真进度条
  nextTick(() => {
    timeScaleInit(DEFALUT_SIM_DURATION, false);
  });
});
const timeScaleInit = (duration: number, status: boolean) => {
  const sectionArr = [
    {
      id: 1,
      duration,
      status,
    },
  ];
  if (!timeScale.value) {
    timeScale.value = timescale.render({
      ele: 'timeScale',
      sectionArr,
      showSectionStatus: true,
      indexEnable: true,
      clipEnable: true,
    });
    timeScale.value.on(
      'getClickTime',
      debounce(() => {
        state.jumpType = 'time';
        state.jumpTo = timeScale.value.getClickTime() / 1000;
        jump();
      }, 100)
    );
  } else {
    timeScale.value.reload(sectionArr);
  }
  setTimeScaleDisable(!state.isPause);
};

const scenarioListRef = ref();
const nodeDesignRef = ref();
const playbackListRef = ref();
const playbakEventListRef = ref();
const speedConfigRef = ref();
const timeInputContentRef = ref();
const unityContainerRef = ref();

const isRefresh = ref(false);
const isRefresh1 = ref(false);
const showSpeedConfig = ref(false);
const unityMounted = ref(false);
const isFullscreen = ref(false);
const timeJumpDisabled = ref(false);
const triggerStop = ref(false); // 手动触发过停止动作
const errorVisible = ref(false); // 错误弹窗
const stepList = [
  {label: '10毫秒', value: 10},
  {label: '50毫秒', value: 50},
  {label: '100毫秒', value: 100},
  {label: '250毫秒', value: 250},
  {label: '500毫秒', value: 500},
  {label: '1000毫秒', value: 1000},
];
const stabilizeStatusAry = [0, 1, 2, 3, 4, 5];

const useContorlState = () => {
  const showJumpBox = ref(false);
  const onFileVisible = ref(false); //推演配置弹窗显隐
  const onFileDescVisible = ref(false); //存档描述弹窗显隐
  const nodeDesignVisible = ref(false); //分布式弹窗显隐
  const state = reactive<StateType>({
    isInit: true,
    isStart: false,
    isPause: false,
    isContinue: false,
    isStop: false,
    isReset: false,

    simProgressSocket: null,
    simStatusConfigSocket: null,
    runningMode: 0, //运行模式 0仿真 1回放
    simTime: 0, //仿真时间
    jumpTo: 0, //跳转到
    jumpType: 'time', //跳转类型，time\progress
    config: {
      archiveId: 0, // 初始化后的存档id;
      mode: false, // 推演模式 -> false事件模式; true帧模式
      replayRecord: false, // 是否开启回放记录
      automatic: false, // 是否开启自动存档
      ifNodeDesign: false, // 是否分布式节点设计
      interval: 0, // 存档间隔
      unit: 0, // 存档间隔单位
      step: 250, // 仿真步长(只有帧模式才有)
    },
    onFileDesc: '', //存档描述
    recordStatus: 0, // 存档时的状态
    currentScenarioId: 0,
    dataTableName: '', // 当前选中的回放
    simTimeRange: 0, // 仿真时长
    taskStatus: 0, // 仿真状态
    timeStamp: 0, // 仿真进度时间戳
  });
  const changeJumpType = () => {
    state.jumpType = state.jumpType === 'time' ? 'progress' : 'time';
  };
  // 获取选择的想定
  const getScenario = async (data: {id: number}) => {
    state.currentScenarioId = data.id;
    const res = (await getScenarioConfig(state.currentScenarioId)).data;
    if (res) {
      state.config.mode = res.mode; // 推演模式
      state.config.replayRecord = res.replayRecord; // 回放记录
      state.config.automatic = res.automatic; // 自动存档
      state.config.interval = res.interval; // 自动存档开启后的存档间隔
      state.config.unit = res.unit; // 存档单位
    } else {
      getGlobalCongigFunc();
    }

    // 使用scenario store获取想定信息和模型列表
    try {
      const info = await scenarioStore.fetchScenarioModels(data.id);
      console.log('🚀 ~ getScenario ~ info:', info);
      console.log('🚀 ~ getScenario ~ models:', scenarioStore.modelList);
    } catch (error) {
      console.error('获取想定信息失败:', error);
    }
  };
  // 获取选择的回放
  const getPlayback = (data: {dataTableName: string; scenarioId: number}) => {
    state.dataTableName = data.dataTableName;
    state.currentScenarioId = data.scenarioId;
    playbakEventListRef.value.listQuery.tableName = data.dataTableName;
  };
  const changePlayback = () => {
    if (state.taskStatus !== 0 && state.taskStatus !== 4 && state.taskStatus !== 5) return;
    state.runningMode = 1;
  };
  const changeSim = () => {
    if (state.taskStatus !== 0 && state.taskStatus !== 4 && state.taskStatus !== 5) return;
    state.runningMode = 0;
  };
  const modeChange = () => {
    timeScaleInit(state.simTimeRange || DEFALUT_SIM_DURATION, !!state.simTimeRange);
  };
  const changeStep = (val: number) => {
    scenarioControlApi({cmd: 7, step: val});
  };
  const handleInterVal = (val: number) => {
    state.config.interval = Math.max(state.config.unit ? 10 : 600, val);
  };
  const handleUnit = () => {
    state.config.interval = Math.max(state.config.unit ? 10 : 600, state.config.interval);
  };
  const openNodedesign = () => {
    nodeDesignVisible.value = true;
  };

  return {showJumpBox, onFileVisible, onFileDescVisible, nodeDesignVisible, state, changePlayback, changeSim, changeJumpType, getScenario, getPlayback, modeChange, changeStep, handleInterVal, handleUnit, openNodedesign};
};
const {showJumpBox, onFileVisible, onFileDescVisible, nodeDesignVisible, state, changePlayback, changeSim, changeJumpType, getScenario, getPlayback, modeChange, changeStep, handleInterVal, handleUnit, openNodedesign} = useContorlState();
watch(
  () => [showSpeedConfig.value, showJumpBox.value],
  newV => {
    if (newV.some(i => i)) {
      document.addEventListener('click', handleDocumentClick);
    } else {
      document.removeEventListener('click', handleDocumentClick);
    }
  }
);
// watch(
//   () => state.simTimeRange,
//   value => {
//     if (state.taskStatus === 5) return;
//     nextTick(() => timeScaleInit(value || DEFALUT_SIM_DURATION, !value));
//   }
// );
// 监听跳转仿真进度条跳转动作
watch(
  () => timeJumpDisabled.value,
  newV => {
    setTimeScaleDisable(newV);
  }
);
watch(
  () => state.isPause,
  newV => {
    setTimeScaleDisable(!newV);
  }
);
// 设置进度条的操作是否可用;
const setTimeScaleDisable = (disable: boolean) => {
  const timeScaleDom = document.getElementById('timeScale');
  const timescaleMainDom = document.getElementById('timescale-main');
  if (timeScaleDom?.style) {
    timeScaleDom.style.cursor = disable ? 'no-drop' : 'unset';
    timeScaleDom.style.opacity = disable ? '.5' : 'unset';
  }
  if (timescaleMainDom?.style) {
    timescaleMainDom.style.pointerEvents = disable ? 'none' : 'unset';
  }
};
// 监听全局点击事件
const handleDocumentClick = (e: Event) => {
  e.preventDefault();
  if (showSpeedConfig.value && speedConfigRef.value && !speedConfigRef.value.getDom().contains(e.target)) {
    showSpeedConfig.value = false;
  }
  if (showJumpBox.value && timeInputContentRef.value && !timeInputContentRef.value.contains(e.target)) {
    showJumpBox.value = false;
  }
};
// 初始化运行sokect
const initSockect = () => {
  initSimProgress();
  initSimStatusConfig();
};
// 初始化获取仿真进度
const initSimProgress = () => {
  state.simProgressSocket = new WebSocket(getSimRealTimeSocket());
  state.simProgressSocket.onmessage = (e: {data: string}) => {
    if (!e.data) return;
    const data = JSON.parse(e.data);
    state.timeStamp = data.timeStamp;
    if (timeScale.value) {
      timeScale.value.seekTo(1, data.timeStamp * 1000);

      if (!isRefresh1.value) {
        timeScale.value.setClickTime(data.timeStamp * 1000);
        isRefresh1.value = true;
      }
      if (timeJumpDisabled.value && data.timeStamp * 1000 >= timeScale.value.getClickTime() && !state.runningMode) {
        timeJumpDisabled.value = false;
      }
    }
  };
  state.simProgressSocket.onerror = () => {
    // window.alert('服务连接超时');
    errorVisible.value = true;
  };
  state.simProgressSocket.onclose = () => {
    // window.alert('连接关闭');
    errorVisible.value = true;
  };
};
// 初始化获取仿真状态、配置
const initSimStatusConfig = () => {
  state.simStatusConfigSocket = new WebSocket(getSimStatusConfigSocket());
  console.log(1233);

  state.simStatusConfigSocket.onmessage = async (e: {data: string}) => {
    if (!e.data) return;
    const data = JSON.parse(e.data);
    // 运行模式
    state.runningMode = data.mode;
    // 仿真总时长
    state.simTimeRange = data.duration;
    // 仿真时间
    state.simTime = data.startTime;
    // 帧模式下的仿真步长
    state.config.step = data.step || 250;
    // 初始化后的存档id
    state.config.archiveId = data.archiveId;
    // 如果是回放, 接收回放的table表名 副标签页用
    if (state.runningMode) state.dataTableName = data.tableName;
    // 想定Id, 副标签页用
    if (data.simulateId && state.currentScenarioId !== data.simulateId) {
      state.currentScenarioId = data.simulateId;
      const res = (await getScenarioConfig(state.currentScenarioId)).data;
      console.log('🚀 ~ state.simStatusConfigSocket.onmessage= ~ res:', res);
      if (res) {
        state.config.mode = res.mode; // 推演模式
        state.config.replayRecord = res.replayRecord; // 回放记录
        state.config.automatic = res.automatic; // 自动存档
        state.config.interval = res.interval; // 自动存档开启后的存档间隔
        state.config.unit = res.unit; // 存档单位
        state.config.ifNodeDesign = res.dsc; //是否开启分布式
      }
      if (state.config.ifNodeDesign) {
        configDataController.nodeDesign = JSON.parse(res.nodeConfig);
      }
    }
    // 仿真状态
    state.taskStatus = data.taskStatus;
    // 仿真倍数
    if (speedConfigRef.value) {
      speedConfigRef.value.speed = data.speed || 1;
      speedConfigRef.value.handleSetSpeed(false, speedConfigRef.value.speed);
    }

    // 仿真状态处理
    if (stabilizeStatusAry.includes(data.taskStatus)) {
      setBtnsStatus(statusList.value[data.taskStatus]);
    }
  };
};
watch(
  () => state.taskStatus,
  async val => {
    if (state.taskStatus === 6) return;
    if (val === 1) {
      onFileVisible.value = false;
      const res = (await getScenarioConfig(state.currentScenarioId)).data;
      if (res) {
        state.config.mode = res.mode; // 推演模式
        state.config.replayRecord = res.replayRecord; // 回放记录
        state.config.automatic = res.automatic; // 自动存档
        state.config.interval = res.interval; // 自动存档开启后的存档间隔
        state.config.unit = res.unit; // 存档单位
      }
    }
    if (val === 4) {
      showSpeedConfig.value = false;
      showJumpBox.value = false;
      speedConfigRef.value.handleSetSpeed(false, 1);
    }
    nextTick(() => timeScaleInit(state.simTimeRange || DEFALUT_SIM_DURATION, !!state.simTimeRange));
  }
);
watch(
  () => state.runningMode,
  () => {
    // 清空想定id和回放记录
    state.currentScenarioId = 0;
    state.dataTableName = '';
    nextTick(() => {
      timeScaleInit(DEFALUT_SIM_DURATION, false);
    });
  }
);
// 设置各个运控按钮的状态（是否禁用）
const setBtnsStatus = (val: {isInit: boolean; isStart: boolean; isPause: boolean; isContinue: boolean; isStop: boolean; isReset: boolean}) => {
  state.isInit = val.isInit;
  state.isStart = val.isStart;
  state.isPause = val.isPause;
  state.isContinue = val.isContinue;
  state.isStop = val.isStop;
  state.isReset = val.isReset;
};
// 运控部分
const socketControl = async (cmd: number) => {
  await scenarioControlApi({
    cmd,
    mode: state.runningMode,
  });
};

// 倍速设置
const changeSpeed = async (val: number) => {
  try {
    await scenarioControlApi({cmd: 4, mode: state.runningMode, speedRatio: val});
  } finally {
    speedConfigRef.value.dragging = false;
  }
};
const reset = () => {
  if (state.isInit) return;
  // 如果是已经开始/暂停时
  if ([2, 3].includes(state.taskStatus) && !state.runningMode) triggerStop.value = true;
  socketControl(3);
  state.simTimeRange = 0;
};

// 时间/百分比进度跳转
const jump = async () => {
  timeJumpDisabled.value = true;
  if (state.jumpType === 'time') {
    state.jumpTo = state.jumpTo > state.simTimeRange ? state.simTimeRange : state.jumpTo;
  } else {
    state.jumpTo = state.jumpTo > 100 ? 100 : state.jumpTo;
    state.jumpTo = state.jumpTo < 0 ? 0 : state.jumpTo;
    // 将百分比转为秒
    state.jumpTo = (state.simTimeRange / 100) * state.jumpTo;
  }
  timeScale.value.setClickTime(state.jumpTo * 1000);
  try {
    await scenarioControlApi({
      cmd: 5,
      mode: state.runningMode,
      scheduleRatio: Number(state.jumpTo),
    });
    if (state.runningMode) timeJumpDisabled.value = false;
  } catch (error) {
    timeJumpDisabled.value = false;
  }

  showJumpBox.value = false;
  state.jumpTo = 0;
};
// 初始化场景
const initSseControl = async () => {
  if (!state.currentScenarioId) return ElMessage.warning('请先选择想定!');
  reset();
  const loading = ElLoading.service({
    lock: true,
    text: `想定初始化...`,
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)',
  });
  if (!state.runningMode) {
    // 想定校验
    const res = (await scenarioVerifyApi(state.currentScenarioId)).data;
    if (!res.script.success) {
      ElMessage.error({message: res.script.errMsg, duration: 1500});
      loading.close();
      return;
    }
  }
  const param = {
    id: state.currentScenarioId,
    mode: state.runningMode,
    tableName: '',
    config: {
      automatic: state.config.automatic,
      interval: state.config.interval,
      mode: state.config.mode,
      replayRecord: state.config.replayRecord,
      unit: state.config.unit,
    },
  };
  if (state.runningMode === 1) param.tableName = state.dataTableName;
  try {
    await scenarioRunInitApi(param);
    ElMessage.success({message: '初始化成功', duration: 1500});
    triggerStop.value = false;
    if (!state.simProgressSocket || !state.simStatusConfigSocket) initSockect();
  } finally {
    loading.close();
  }
};

// 点击断点存档
const toSaveFile = () => {
  state.recordStatus = state.taskStatus;
  // 如果是正在运行中, 暂停推演
  if (state.isPause) socketControl(1);
  state.onFileDesc = '';
  onFileDescVisible.value = true;
};

// 保存断点存档
const saveRecordFile = async () => {
  const params = {
    archiveId: state.config.archiveId, // 存档记录
    automatic: false, // 是否自动存档 -> 手动
    description: state.onFileDesc, // 存档描述
    mode: state.config.mode, // 推演模式
    simTime: timer.dateFormatting(state.simTime), // 仿真时间
    speed: speedConfigRef.value?.speed || 0, // 仿真倍速
    step: state.config.mode ? state.config.step : undefined, // 仿真步长
    timeStamp: state.timeStamp, // 仿真进度时间戳
  };
  await addKeepFileToList(params);
  if (state.recordStatus === 2) socketControl(2);
  state.recordStatus = 0;
  onFileDescVisible.value = false;
  ElMessage.success({message: '存档成功', duration: 1500});
};

// 取消断点存档
const cancelSaveRecord = async () => {
  socketControl(2);
  onFileDescVisible.value = false;
};

// 双击初始化想定
const handleDblInitScenario = (data: {id: number}) => {
  state.currentScenarioId = data.id;
  initSseControl();
};

const changeRefresh = () => {
  isRefresh.value = false;
};
// 双击初始化回放
const handleDblInitPlayback = (data: {dataTableName: string; scenarioId: number}) => {
  state.dataTableName = data.dataTableName;
  state.currentScenarioId = data.scenarioId;
  playbakEventListRef.value.listQuery.tableName = data.dataTableName;
  initSseControl();
};
// 关闭/取消推演配置
const resetOnfileConfig = async () => {
  // 查询最新的推演配置
  let res;
  if (state.currentScenarioId) {
    res = (await getScenarioConfig(state.currentScenarioId)).data;
  } else {
    res = (await getGlobalConfig()).data;
  }
  state.config.mode = res.mode; // 推演模式
  state.config.replayRecord = res.replayRecord; // 回放记录
  state.config.automatic = res.automatic; // 自动存档
  state.config.interval = res.interval; // 自动存档开启后的存档间隔
  state.config.unit = res.unit; // 存档单位
  state.config.ifNodeDesign = res.dsc; //是否开启分布式
  if (state.config.ifNodeDesign) {
    configDataController.nodeDesign = JSON.parse(res.nodeConfig);
  }
  onFileVisible.value = false;
};
// 保存推演配置
const setOnfileConfig = async () => {
  const params = {mode: state.config.mode, replayRecord: state.config.replayRecord, automatic: state.config.automatic, interval: state.config.interval, unit: state.config.unit, id: state.currentScenarioId, dsc: state.config.ifNodeDesign, nodeConfig: ''};
  if (state.config.ifNodeDesign) {
    params.nodeConfig = JSON.stringify(configDataController.nodeDesign);
  }
  if (state.currentScenarioId) await setScenarioConfig(state.currentScenarioId, params); // 想定推演配置
  else await setGlobalConfig(params); // 全局推演配置
  ElMessage.success({message: '配置成功', duration: 1500});
  onFileVisible.value = false;
};
const closeNodeDesignBox = () => {
  nodeDesignVisible.value = false;
};
// 获取全局推演配置
const getGlobalCongigFunc = async () => {
  const res = (await getGlobalConfig()).data;
  state.config.mode = res.mode; // 推演模式
  state.config.replayRecord = res.replayRecord; // 回放记录
  state.config.automatic = res.automatic; // 自动存档
  state.config.interval = res.interval; // 自动存档开启后的存档间隔
  state.config.unit = res.unit; // 存档单位
};

// 全屏
const handleFullscreen = () => {
  const dom = unityContainerRef.value;
  if (!dom) return;
  if (!document.fullscreenElement) dom.requestFullscreen();
  else document.exitFullscreen();
  isFullscreen.value = !document.fullscreenElement;
};

// 开启仿真跳转配置
const handleJumpConfig = () => {
  if (!state.isPause) return;
  showJumpBox.value = !showJumpBox.value;
};

// 刷新页面
const handleWindowReload = () => {
  window.location.reload();
};
</script>

<template>
  <el-row class="header f-14">
    <el-row class="left align-items">
      <div :class="[state.taskStatus == 0 || state.taskStatus == 4 || state.taskStatus == 5 ? '' : 'disabled', 'types']">
        <!-- <div :class="[state.simTimeRange === 0 ? '' : '', 'types']"> -->
        <span :class="[!state.runningMode ? 'active' : '', 'sim']" @click="changeSim"
          ><i :class="['icon', !state.runningMode ? 'sim_light' : 'sim_dark']"></i>
          <p>推演</p></span
        >
        <span :class="[state.runningMode ? 'active' : '', 'backup']" @click="changePlayback"
          ><i :class="['icon', state.runningMode ? 'back_light' : 'back_dark']"></i>
          <p>回放</p></span
        >
      </div>
      <div :class="[state.taskStatus == 0 || state.taskStatus == 4 || state.taskStatus == 5 ? '' : 'disabled', 'types-btn']">
        <el-button v-if="!state.runningMode" class="item-btn" :disabled="!state.isInit" @click="scenarioListRef.dialogVisible = true">想定列表</el-button>
        <el-button v-else class="item-btn" :disabled="!state.isInit" @click="playbackListRef.dialogVisible = true">回放列表</el-button>
      </div>
      <div class="divider_public divider mar_h_10"></div>
      <div class="controlBox">
        <div class="controlBtn">
          <el-button :disabled="!state.isInit || !state.currentScenarioId" title="初始化" @click="initSseControl">
            <i :class="!state.isInit || !state.currentScenarioId ? 'init' : 'init_light'"></i>
          </el-button>
        </div>
        <div class="controlBtn">
          <el-button :disabled="!state.isStart" title="开始" @click="socketControl(0)">
            <i :class="state.isStart ? 'begin_light' : 'begin'"></i>
          </el-button>
        </div>
        <div class="controlBtn">
          <el-button :disabled="!state.isPause" title="暂停" @click="socketControl(1)">
            <i :class="state.isPause ? 'pause_light' : 'pause'"></i>
          </el-button>
        </div>
        <div class="controlBtn">
          <el-button :disabled="!state.isContinue" title="继续" @click="socketControl(2)">
            <i :class="state.isContinue ? 'continue_light' : 'continue'"></i>
          </el-button>
        </div>
        <div class="controlBtn">
          <el-button :disabled="!state.isStop" title="停止" @click="reset">
            <i :class="state.isStop ? 'finish_light' : 'finish'"></i>
          </el-button>
        </div>
      </div>
      <div class="divider_public divider mar_h_10"></div>
      <el-row class="speed-box p-r">
        <el-button title="倍速设置" :disabled="!state.isPause" @click.stop="showSpeedConfig = !showSpeedConfig">
          <i :class="state.isPause ? 'speed_light' : 'speed'"></i>
          <span v-if="speedConfigRef" class="mar_l_5 speed-value overflow_hidden">x {{ speedConfigRef.speed }}</span>
        </el-button>
        <el-row v-show="showSpeedConfig" class="speed-config-box">
          <SpeedConfig ref="speedConfigRef" @change="changeSpeed" />
        </el-row>
      </el-row>
      <div class="divider_public divider mar_h_10"></div>
      <div v-show="state.config.mode && !state.runningMode" class="step-box mar_r_10">
        <el-form-item label="步长" :style="{marginBottom: '0', opacity: !state.isPause ? '.5' : 'unset'}">
          <el-select v-model="state.config.step" :disabled="!state.isPause" filterable :style="{width: '90px'}" @change="changeStep">
            <el-option v-for="item in stepList" :key="item.label" :value="item.value">{{ item.label }}</el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="sim-config">
        <el-button v-show="state.runningMode" :disabled="![2, 3].includes(state.taskStatus)" class="back-event-list" @click="playbakEventListRef.dialogVisible = true">回放事件列表</el-button>
        <el-button v-show="!state.runningMode" :style="{marginLeft: 0}" :disabled="state.taskStatus !== 0 && state.taskStatus !== 4" @click="onFileVisible = true">推演配置</el-button>
        <el-button v-show="!state.runningMode" :disabled="!(state.taskStatus == 2 || state.taskStatus == 3)" @click="toSaveFile">断点存档</el-button>
      </div>
      <div class="divider_public divider mar_h_10"></div>
      <el-button :disabled="!state.isPause" title="进度跳转" class="time-box">
        <i :class="state.isPause ? 'jump_light' : 'jump'" @click.stop="handleJumpConfig"></i>
        <div v-show="showJumpBox" ref="timeInputContentRef" class="time-input-content">
          <p @click="changeJumpType">{{ state.jumpType === 'time' ? '时间' : '进度' }}:</p>
          <el-input v-model="state.jumpTo" type="number" :style="{height: '25px'}">
            <template #append>{{ state.jumpType === 'time' ? '秒' : '%' }}</template>
          </el-input>
          <el-button :disabled="!state.isPause" @click="jump">确定</el-button>
          <el-button @click="showJumpBox = false">取消</el-button>
        </div>
      </el-button>
    </el-row>
    <div id="timeScale" class="timeScale"></div>
    <el-row class="right">
      <span>仿真</span>
      <span>时间</span>
    </el-row>
  </el-row>

  <!-- 推演配置弹窗 -->
  <el-dialog v-model="onFileVisible" draggable title="推演配置" center class="on-file" width="400px" :close-on-click-modal="false" @close="resetOnfileConfig">
    <div class="onfile-form-box">
      <el-form :model="state.config" label-width="90px">
        <el-form-item label="推演选择:" class="form-item">
          <el-radio-group v-model="state.config.mode" @change="modeChange">
            <el-radio :label="false">事件模式</el-radio>
            <el-radio :label="true">帧模式</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="回放记录:" class="form-item">
          <el-switch v-model="state.config.replayRecord" active-text="开" inactive-text="关"> </el-switch>
        </el-form-item>
        <el-form-item label="自动存档:" class="form-item">
          <el-switch v-model="state.config.automatic" active-text="开" inactive-text="关"> </el-switch>
        </el-form-item>
        <el-form-item v-if="state.config.automatic" label="存档间隔:" class="form-item">
          <el-col :span="17"><el-input v-model.number="state.config.interval" type="number" @change="handleInterVal"> </el-input></el-col>
          <el-col :span="7"
            ><el-select v-model="state.config.unit" @change="handleUnit">
              <el-option :value="0" label="秒">秒</el-option>
              <el-option :value="1" label="分">分</el-option>
            </el-select></el-col
          >
        </el-form-item>
        <el-form-item label="节点设计:" class="form-item">
          <el-switch v-model="state.config.ifNodeDesign" active-text="开" inactive-text="关"> </el-switch>
          &nbsp;&nbsp;&nbsp;
          <el-button v-if="state.config.ifNodeDesign" @click="openNodedesign">分布式设计</el-button>
        </el-form-item>
      </el-form>
      <el-row class="dis-flex flex-x-center mar_b_10 mar_t_10 dialog-footer">
        <el-button @click="resetOnfileConfig">取&nbsp;消</el-button>
        <el-button @click="setOnfileConfig">保&nbsp;存</el-button>
      </el-row>
    </div>
  </el-dialog>
  <!-- 存档描述弹窗 -->
  <el-dialog v-model="onFileDescVisible" title="存档描述" center class="on-file" width="392px" :close-on-click-modal="false">
    <div>
      <el-input v-model="state.onFileDesc" type="textarea" :autosize="{minRows: 8}"></el-input>
    </div>
    <el-row class="dis-flex flex-x-center mar_b_10 mar_t_10 dialog-footer"><el-button @click="cancelSaveRecord">取&nbsp;消</el-button> <el-button @click="saveRecordFile">保&nbsp;存</el-button></el-row>
  </el-dialog>
  <!-- /分布式节点设计 -->
  <el-dialog v-model="nodeDesignVisible" draggable title="分布式设计" top="60px" center class="on-file" width="880px" :close-on-click-modal="false" @close="closeNodeDesignBox">
    <NodeDesign ref="nodeDesignRef" @change-refresh="changeRefresh" />
  </el-dialog>
  <!-- 想定列表 -->
  <ScenarioList ref="scenarioListRef" @receive-data="getScenario" @confim-init="handleDblInitScenario" @change-refresh="changeRefresh" />
  <!-- 想定列表 -->
  <PlaybackList ref="playbackListRef" @receive-data="getPlayback" @confim-init="handleDblInitPlayback" />
  <!-- 回放事件列表 -->
  <PlaybakEventList ref="playbakEventListRef" />
  <div ref="unityContainerRef" class="unityclass">
    <img v-if="unityMounted" class="full-screen pointer" src="/images/full_screen_icon.png" alt="" :title="isFullscreen ? '退出全屏' : '全屏'" width="22" @click="handleFullscreen" />
    <!-- <UnityVue :unity="unityContext" /> -->
    <iframe :src="urlRef" class="iframe-swagger" frameborder="0"></iframe>
  </div>
  <!-- 断开连接错误弹窗 -->
  <!-- <el-dialog v-model="errorVisible" title="错误" align-center width="18%" :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false">
    <el-row class="flex-dir-column align-items pad_t_20 error-box">
      <img src="/images/error_img.png" alt="" class="mar_b_20" />
      <p class="text_align_c pad_h_30 mar_b_20 text">连接后台服务失败，请检查服务状态后点击刷新按钮重试</p>
      <el-button class="mar_b_20 reload-btn" :icon="RefreshRight" @click="handleWindowReload">刷新</el-button>
    </el-row>
  </el-dialog> -->
</template>
<style scoped lang="less">
.header {
  display: flex;
  height: 40px;
  padding-left: 3px;
  margin-top: 3px;
  color: #badffc;
  align-items: center;

  .left {
    height: 40px;
    padding: 0 5px;
    margin-right: 2px;
    // width: calc(100% - 55% - 43px - 3px);
    background: #004368;
    border: 1px solid #36b6ff;
    border-radius: 3px;

    .types {
      display: flex;

      > span {
        display: flex;
        height: 27px;
        // width: 70px;
        padding: 0 10px;
        text-align: center;
        border: 1px solid #36b6ff;
        cursor: pointer;
        align-items: center;
        justify-content: center;
      }

      .icon {
        display: inline-block;
        width: 14px;
        height: 14px;
        margin-right: 3px;
      }

      .sim {
        border-bottom-left-radius: 2px;
        border-top-left-radius: 2px;
        border-right: 0;
      }

      .backup {
        border-bottom-right-radius: 2px;
        border-top-right-radius: 2px;
      }
    }

    .types-btn {
      margin-left: 10px;

      // button {
      //   width: 80px;
      // }
    }

    .controlBox .el-button {
      background: transparent;
      border: 0;

      i {
        display: inline-block;
        width: 25px;
        height: 25px;
      }
    }

    .active {
      color: #fff;
      background-image: unset !important;
      background-color: #2aa8f0;
    }

    .controlBox {
      display: flex;

      .controlBtn {
        .el-button {
          padding: 0;
        }
      }

      .controlBtn:not(:last-child) {
        margin-right: 7px;
      }
    }

    .sim-config {
      display: flex;
      width: 200px;
      justify-content: space-between;

      button {
        width: 49%;
      }

      .back-event-list {
        width: 100%;
      }
    }

    .divider {
      width: 2px;
      height: 25px;
    }

    .speed-box {
      position: relative;

      /* stylelint-disable-next-line no-descending-specificity */
      .el-button {
        padding: 0;
        background: transparent;
        border: 0;

        i {
          display: inline-block;
          width: 25px;
          height: 25px;
        }
      }

      .speed-config-box {
        position: absolute;
        top: 37px;
        left: 5px;
        z-index: 1;

        .el-input {
          height: 24px;
        }
      }

      .speed-value {
        display: inline-block;
        max-width: 60px;
        min-width: 55px;
        font-size: 12px;
        line-height: 23px;
        text-align: center;
        border: var(--border);
        border-radius: 2px;
      }

      .speedChangeBox {
        position: absolute;
        top: 30px;
        right: 5px;

        .el-select__popper.el-popper[role='tooltip'] {
          display: block !important;
        }
      }

      :deep(.el-select) .el-input__wrapper {
        height: 27px;
      }

      :deep(.el-select) .el-select__wrapper {
        height: 27px;
      }

      :deep(.el-form-item__label) {
        padding-right: 5px;
        font-size: 14px !important;
      }
    }

    .step-box {
      :deep(.el-form-item__label) {
        padding-right: 5px;
        font-size: 14px !important;
        color: #badffc;
      }

      :deep(.el-select) .el-select__wrapper {
        height: 27px;
        min-height: unset;
      }
    }

    .time-box {
      position: relative;
      padding: 0;
      background: transparent;
      border: 0;

      /* stylelint-disable-next-line no-descending-specificity */
      i {
        display: inline-block;
        width: 25px;
        height: 25px;
      }

      .time-input-content {
        position: absolute;
        top: 40px;
        left: 5px;
        z-index: 1;
        display: flex;
        width: 300px;
        height: 25px;
        align-items: center;

        p {
          width: 75px;
          height: 23px;
          margin-right: 2px;
          line-height: 23px;
          text-align: center;
          border: var(--border);
          cursor: pointer;
          background-image: linear-gradient(0deg, #073b5a 0%, #022f49 100%);
        }

        .el-input {
          width: 60%;
        }

        .el-button {
          height: 25px;
          margin-left: 2px !important;
        }
      }
    }
  }

  .timeScale {
    flex: 1;
    height: 40px;
    background: #004368;
  }

  .right {
    display: flex;
    width: 40px;
    height: 38px;
    padding: 3px 0;
    margin-left: 3px;
    background: #004368;
    border: solid 1px #36b6ff;
    border-radius: 3px;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
  }
}

.on-file {
  .deduceConfigItem {
    margin: 15px 0;

    .on-file-label {
      display: inline-block;
      width: 70px;
      margin-right: 5px;
      color: #fff !important;
    }

    .on-file-interval-box {
      display: flex;
      height: 25px;
    }

    /* stylelint-disable-next-line no-descending-specificity */
    .el-input {
      width: 60%;
      // margin: 0 5px;
    }

    .el-select {
      width: 15%;
    }
  }
}

.unityclass {
  position: relative;
  width: 100%;
  height: calc(100% - 43px);
  background: #032a45;

  .iframe-swagger {
    width: 100%;
    height: calc(100vh - 40px);
    border: none;
  }

  .full-screen {
    position: absolute;
    top: 5px;
    left: calc(13.1% + 7px);
  }

  canvas {
    display: block;
    user-select: none;
  }

  canvas:focus-visible {
    outline: unset;
  }
}

.onfile-form-box {
  .el-form {
    border-left: solid 1px #0b5d92;
    border-right: solid 1px #0b5d92;
    background: #013758;
  }

  .form-item {
    padding: 5px 27px;
    margin-bottom: 5px;
    background: #043f61;
    border-top: solid 1px #0b5d92;
    border-bottom: solid 1px #0b5d92;
  }
}

.error-box {
  .text {
    line-height: 1.5;
  }

  .reload-btn {
    width: 100px;
    height: 30px;
    font-size: 16px;
  }
}
</style>
