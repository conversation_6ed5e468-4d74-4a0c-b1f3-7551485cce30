/*
 * <AUTHOR> laofan
 * @Date         : 2023-02-24 09:53:51
 * @LastEditors: liukun
 * @LastEditTime: 2025-02-21 10:36:31
 * @Description  : 请填写简介
 */
import {defineConfig} from 'vite';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig({
  plugins: [vue()],
  server: {
    host: '0.0.0.0',
    port: 8686, //这里自行更改
    open: false, //是否打开浏览器
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    outDir: 'situation3D',
  },
  base: './',
});
