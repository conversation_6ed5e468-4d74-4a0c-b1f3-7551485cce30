<!--
 * @Description: 回放列表
 * @Author: tzs
 * @Date: 2024-09-11 11:46:03
 * @LastEditors: liukun
 * @LastEditTime: 2024-10-14 13:37:39
-->
<script setup lang="ts">
import {reactive, ref, watch} from 'vue';
import {getPlaybackEventListApi, goEventTimeApi} from '@/api/simulation';
import {ElMessage} from 'element-plus';
import {PlayBackEventType} from '@/types/index';
const emits = defineEmits(['receiveData']);

const eventList = ref<PlayBackEventType[]>([]);
const dialogVisible = ref(false);
const timeStampValue = ref(0);
const listQuery = reactive({
  tableName: '',
  filter: [],
  type: '',
});
const searchEventOptions = ref([]);
watch(
  () => dialogVisible.value,
  newV => {
    if (!newV) return;
    listQuery.filter = [];
    listQuery.type = '';
    timeStampValue.value = 0;
    getList();
  }
);
const handleTypeChange = () => {
  listQuery.filter = [];
  getList();
};
const getList = async () => {
  timeStampValue.value = 0;
  const {data} = await getPlaybackEventListApi(listQuery);
  eventList.value = data.events || [];
  searchEventOptions.value = data?.types || data?.entity || [];
};
// 确认
const confim = async () => {
  await goEventTimeApi({cmd: 5, scheduleRatio: timeStampValue.value});
  ElMessage.success('跳转成功');
  emits('receiveData', '');
  dialogVisible.value = false;
};
const clearType = () => {
  listQuery.filter = [];
  getList();
};
defineExpose({dialogVisible, listQuery});
</script>

<template>
  <el-dialog v-model="dialogVisible" title="回放事件列表" width="36.6%" draggable :show-close="false" :close-on-click-modal="false" destroy-on-close align-center>
    <el-form label-width="90px">
      <el-row>
        <el-col :span="12"
          ><el-form-item label="类型筛选">
            <el-select v-model="listQuery.type" clearable @change="handleTypeChange" @clear="clearType">
              <el-option value="event" label="事件" />
              <el-option value="entity" label="实体" />
            </el-select> </el-form-item
        ></el-col>
        <el-col :span="12"
          ><el-form-item label="筛选条件">
            <!-- collapse-tags collapse-tags-tooltip -->
            <el-select v-model="listQuery.filter" collapse-tags collapse-tags-tooltip multiple clearable @change="getList" @clear="getList" @remove-tag="getList">
              <el-option v-for="item in searchEventOptions" :key="item" :value="item" :label="item" />
            </el-select> </el-form-item
        ></el-col>
      </el-row>
    </el-form>
    <div v-if="eventList.length" class="content-box">
      <div class="timeline-box pad_5">
        <el-timeline>
          <el-timeline-item v-for="item in eventList" :key="item.timeStamp" :timestamp="item.timeStamp.toString()" placement="top">
            <!-- 事件内容 -->
            <div class="event-box">
              <el-row v-for="(eItem, eIndex) in item.event" :key="eItem" class="event-item pointer" :style="{background: timeStampValue === item.timeStamp ? '#005586' : ''}" @click="timeStampValue = item.timeStamp">
                <div class="event-name pad_h_5 overflow_hidden" :style="{lineHeight: `${eItem.infos.length * 34 - 1}px`, borderBottom: eIndex === item.event.length - 1 ? 'unset' : '1px solid #1c8fd0'}" :title="eItem.type">
                  {{ eItem.type }}
                </div>
                <div class="event-content" :style="{borderBottom: eIndex === item.event.length - 1 ? 'unset' : '1px solid #1c8fd0'}">
                  <el-row v-for="(iItem, iIndex) in eItem.infos" :key="iItem" class="name-list" :style="{borderBottom: iIndex === eItem.infos.length - 1 ? 'unset' : '1px solid #1c8fd0'}">
                    <p
                      v-for="(value, key, vIndex) in iItem"
                      :key="value"
                      :style="{width: `${100 / Object.keys(iItem).length}%`, borderRight: Object.keys(iItem).length === 1 || vIndex === Object.keys(iItem).length - 1 ? 'unset' : '1px solid #1c8fd0'}"
                      class="name-item pad_h_5 overflow_hidden"
                      :title="value"
                    >
                      {{ value }}
                    </p>
                  </el-row>
                </div>
              </el-row>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </div>
    <template v-else>
      <div class="empty-box dis-flex flex-dir-column align-items flex-x-center">
        <img src="/images/empty_img.png" alt="" :style="{scale: '0.9'}" />
        <p class="empty-text mar_t_10">暂无更多信息</p>
      </div>
    </template>

    <el-row class="dis-flex flex-x-center mar_b_10 mar_t_10 dialog-footer">
      <el-button @click="dialogVisible = false">取&nbsp;消</el-button>
      <el-button :disabled="!timeStampValue" @click="confim">跳&nbsp;转</el-button>
    </el-row>
  </el-dialog>
</template>
<style lang="less" scoped>
.search-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;

  span {
    display: inline-block;
    margin-right: 5px;
    color: #fff;
  }
}

.content-box {
  height: 570px;

  .timeline-box {
    height: 100%;
    margin-right: 5px;
    overflow: auto;

    .timeline-item {
      color: var(--font-color-white);
    }

    .el-timeline-item {
      padding-bottom: 15px;

      :deep(.el-timeline-item__tail) {
        border-left: 1px solid #2080b7;
        left: 7px;
      }

      :deep(.el-timeline-item__node) {
        left: 0;
        width: 16px;
        height: 16px;
        background: #0a4f76;
        border: solid 1px #36b6ff;
      }

      :deep(.el-timeline-item__wrapper) {
        top: -2px;
        padding-left: 23px;

        .el-timeline-item__timestamp {
          color: var(--font-color-white);
        }

        .el-timeline-item__content {
          padding-left: 12px;
          color: #fff;

          .event-box {
            background: #053957;
            border: 1px solid #1c8fd0;
            border-radius: 4px;

            .event-item {
              .event-name {
                width: 15%;
                text-align: center;
                border-right: 1px solid #1c8fd0;
              }

              .event-content {
                width: 85%;

                .name-list {
                  height: 34px;
                  text-align: center;

                  .name-item {
                    line-height: 34px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .table-right {
    p {
      height: 35px;
      margin: 0;
      line-height: 35px;
    }

    .weapon-box {
      display: flex;

      span {
        height: 100%;
        flex: 1;
        text-align: center;
      }

      span:first-child {
        border-right: 1px solid #1377b0;
      }
    }
  }
}

:deep(.el-table__cell) {
  height: 100%;
  border: 1px solid #1377b0;
  border-left: 0;
}

:deep(.el-table__cell:first-child) {
  border-left: solid 1px #0f6ca5;
}

:deep(.el-table__cell:last-child) {
  border-right: solid 1px #0f6ca5;
}

:deep(.el-table__row:last-child .el-table__cell) {
  border-bottom: 1px solid #1377b0;
}

.empty-box {
  height: 570px;

  .empty-text {
    font-family: Normal;
    color: #999;
  }
}
</style>
