/*
 * <AUTHOR> laofan
 * @Date         : 2023-02-24 11:20:15
 * @LastEditors: 老范
 * @LastEditTime: 2025-05-16 10:35:32
 * @Description  : 全局路径配置文件
 */
const globalConfig = {
  //home
  BaseResourceUrl: 'http://**************:38080', //资源服务接口地址
  ModelingToolUrl: 'http://************:9091', // 建模工具
  ResourceToolUrl: 'http://************:9092', // 资源工具 异构模型
  ScenarioToolUrl: 'http://*************:8686', // 想定设计
  Simulation3DToolUrl: 'http://************:8689', // 仿真推演
  ExperimentToolUrl: 'http://************:8081', // 实验管理工具
  EvaluationToolUrl: 'http://127.0.0.1:7081', // 效能评估

  //模型
  WizardExeURL: 'http://localhost:8756', // 调试脚本服务地址

  //想定设计
  PreviewControlUrl: 'http://************:8838', //预演运控
  ControlUrl3D: 'http://**************:38838', //三维运控服务http接口地址
  ReviewBaseWSUrl: 'ws://127.0.0.1:38838', //预演socket
  LocalRun: 'http://************:8756', // (本地运行调试)
  MapUrl: 'http://*************:9000/sprite_scenario', // (地图地址)

  //三维
  ControlWSURL3D: 'ws://**************:38838', //三维运控服务socket地址

  //试验设计
  ExperimentUrl: 'http://************:8182', // 实验数据接口
  ExperimentWsUrl: 'ws://************:8182', //实验管理socket

  //评估
  BaseEvaluationUrl: 'http://127.0.0.1:38088', //效能评估服务
  LocalDataUrl: 'http://127.0.0.1:38088', //本地数据获取地址
};
