/*
 * @Description: 想定相关状态管理
 * @Author: AI Assistant
 * @Date: 2025-06-20
 * @LastEditors: 老范
 * @LastEditTime: 2025-06-20 15:26:32
 */

import {defineStore} from 'pinia';
import {ref, computed} from 'vue';
import type {ScenarioModelType, ScenarioType} from '@/types/index';
import {getScenarioInfoApi} from '@/api/simulation';

// 定义树节点结构（与nodeDesign.vue保持一致）
interface TreeNode {
  id: string;
  name: string;
  type: string;
  level: number; // 层级：1-阵营，2-类别，3-实例
  children?: TreeNode[];
}

export const useScenarioStore = defineStore('scenario', () => {
  // 当前选中的想定信息
  const currentScenario = ref<ScenarioType | null>(null);

  // 当前想定的模型列表
  const modelList = ref<ScenarioModelType[]>([]);

  // 转换后的树数据结构
  const treeData = ref<TreeNode[]>([]);

  // 加载状态
  const loading = ref(false);

  // 错误信息
  const error = ref<string | null>(null);

  // 计算属性：按类型分组的模型
  const modelsByType = computed(() => {
    const grouped: Record<string, ScenarioModelType[]> = {};
    modelList.value.forEach(model => {
      const type = model.type || 'unknown';
      if (!grouped[type]) {
        grouped[type] = [];
      }
      grouped[type].push(model);
    });
    return grouped;
  });

  // 计算属性：按类别分组的模型
  const modelsByCategory = computed(() => {
    const grouped: Record<string, ScenarioModelType[]> = {};
    modelList.value.forEach(model => {
      const category = model.category || 'uncategorized';
      if (!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(model);
    });
    return grouped;
  });

  // 计算属性：模型总数
  const modelCount = computed(() => modelList.value.length);

  /**
   * 设置当前想定
   * @param scenario 想定信息
   */
  const setCurrentScenario = (scenario: ScenarioType | null) => {
    currentScenario.value = scenario;
    if (!scenario) {
      // 清空模型列表
      modelList.value = [];
    }
  };

  /**
   * 获取想定信息并提取模型列表
   * @param scenarioId 想定ID
   */
  const fetchScenarioModels = async (scenarioId: number) => {
    try {
      loading.value = true;
      error.value = null;

      const response = await getScenarioInfoApi(scenarioId);
      const scenarioInfo = response.data;

      // 设置当前想定信息
      if (scenarioInfo) {
        currentScenario.value = scenarioInfo;

        // 提取models字段
        if (scenarioInfo.models && Array.isArray(scenarioInfo.models)) {
          modelList.value = scenarioInfo.models;
          // 转换为树数据结构
          convertModelsToTreeData();
        } else {
          modelList.value = [];
          treeData.value = [];
          console.warn('想定信息中未找到models字段或models不是数组');
        }
      }

      return scenarioInfo;
    } catch (err: any) {
      error.value = err.message || '获取想定信息失败';
      modelList.value = [];
      throw err;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 设置模型列表
   * @param models 模型列表
   */
  const setModelList = (models: ScenarioModelType[]) => {
    modelList.value = models || [];
    // 转换为树数据结构
    convertModelsToTreeData();
  };

  /**
   * 添加模型
   * @param model 模型信息
   */
  const addModel = (model: ScenarioModelType) => {
    const existingIndex = modelList.value.findIndex(m => m.id === model.id);
    if (existingIndex >= 0) {
      // 更新现有模型
      modelList.value[existingIndex] = model;
    } else {
      // 添加新模型
      modelList.value.push(model);
    }
  };

  /**
   * 删除模型
   * @param modelId 模型ID
   */
  const removeModel = (modelId: number) => {
    const index = modelList.value.findIndex(m => m.id === modelId);
    if (index >= 0) {
      modelList.value.splice(index, 1);
    }
  };

  /**
   * 更新模型
   * @param modelId 模型ID
   * @param updates 更新的字段
   */
  const updateModel = (modelId: number, updates: Partial<ScenarioModelType>) => {
    const index = modelList.value.findIndex(m => m.id === modelId);
    if (index >= 0) {
      modelList.value[index] = {...modelList.value[index], ...updates};
    }
  };

  /**
   * 根据ID获取模型
   * @param modelId 模型ID
   */
  const getModelById = (modelId: number): ScenarioModelType | undefined => {
    return modelList.value.find(m => m.id === modelId);
  };

  /**
   * 根据类型获取模型列表
   * @param type 模型类型
   */
  const getModelsByType = (type: string): ScenarioModelType[] => {
    return modelList.value.filter(m => m.type === type);
  };

  /**
   * 根据类别获取模型列表
   * @param category 模型类别
   */
  const getModelsByCategory = (category: string): ScenarioModelType[] => {
    return modelList.value.filter(m => m.category === category);
  };

  /**
   * 清空所有数据
   */
  const clearAll = () => {
    currentScenario.value = null;
    modelList.value = [];
    error.value = null;
    loading.value = false;
  };

  /**
   * 将模型列表转换为树数据结构
   * 根据模型的 camp、category、type 等字段构建三级树结构
   */
  const convertModelsToTreeData = () => {
    if (!modelList.value || modelList.value.length === 0) {
      treeData.value = [];
      return;
    }

    // 用于存储分组数据
    const campGroups: Record<string, Record<string, ScenarioModelType[]>> = {};

    // 按阵营和类别分组
    modelList.value.forEach(model => {
      // 确定阵营（从 params 数组中提取 side 参数）
      let camp = 'neutral'; // 默认值

      // 优先从 camp 字段获取
      if (model.camp) {
        camp = model.camp;
      }
      // 从 params 数组中查找 name 为 "side" 的参数
      else if (model.params && Array.isArray(model.params)) {
        const sideParam = model.params.find(param => param.name === 'side');
        if (sideParam && sideParam.value) {
          camp = sideParam.value;
        }
      }
      // 确定类别（优先使用 categoryName 字段，然后是 category，最后是 type）
      const category = model.categoryName || model.category || model.type || 'unknown';

      if (!campGroups[camp]) {
        campGroups[camp] = {};
      }
      if (!campGroups[camp][category]) {
        campGroups[camp][category] = [];
      }
      campGroups[camp][category].push(model);
    });

    // 构建树结构
    const tree: TreeNode[] = [];

    Object.entries(campGroups).forEach(([camp, categories]) => {
      // 创建阵营节点
      const campNode: TreeNode = {
        id: `${camp}_camp`,
        name: getCampDisplayName(camp),
        type: 'camp',
        level: 1,
        children: [],
      };

      // 创建类别节点
      Object.entries(categories).forEach(([category, models]) => {
        const categoryNode: TreeNode = {
          id: `${camp}_${category}`,
          name: getCategoryDisplayName(category),
          type: 'category',
          level: 2,
          children: [],
        };

        // 创建模型节点
        models.forEach(model => {
          const modelNode: TreeNode = {
            id: `${camp}_${category}_${model.id}`,
            name: model.name,
            type: model.type,
            level: 3,
          };
          categoryNode.children!.push(modelNode);
        });

        campNode.children!.push(categoryNode);
      });

      tree.push(campNode);
    });

    treeData.value = tree;
  };

  /**
   * 获取阵营显示名称
   */
  const getCampDisplayName = (camp: string): string => {
    const campNames: Record<string, string> = {
      red: '红方阵营',
      blue: '蓝方阵营',
      neutral: '中立阵营',
      unknown: '未知阵营',
    };
    return campNames[camp] || `${camp}阵营`;
  };

  /**
   * 获取类别显示名称
   */
  const getCategoryDisplayName = (category: string): string => {
    const categoryNames: Record<string, string> = {
      // 英文类别名称
      aircraft: '飞机',
      ship: '舰船',
      missile: '导弹',
      tank: '坦克',
      vehicle: '车辆',
      radar: '雷达',
      satellite: '卫星',
      submarine: '潜艇',
      helicopter: '直升机',
      fighter: '战斗机',
      bomber: '轰炸机',
      transport: '运输机',
      destroyer: '驱逐舰',
      cruiser: '巡洋舰',
      frigate: '护卫舰',
      carrier: '航空母舰',
      corvette: '护卫艇',
      patrol: '巡逻艇',
      landing: '登陆舰',
      supply: '补给舰',
      air_defense: '防空导弹',
      anti_ship: '反舰导弹',
      anti_submarine: '反潜导弹',
      cruise: '巡航导弹',
      ballistic: '弹道导弹',
      torpedo: '鱼雷',
      unknown: '其他',

      // 中文类别名称（如果 categoryName 直接是中文）
      飞机: '飞机',
      舰船: '舰船',
      导弹: '导弹',
      坦克: '坦克',
      车辆: '车辆',
      雷达: '雷达',
      卫星: '卫星',
      潜艇: '潜艇',
      直升机: '直升机',
      战斗机: '战斗机',
      轰炸机: '轰炸机',
      运输机: '运输机',
      驱逐舰: '驱逐舰',
      巡洋舰: '巡洋舰',
      护卫舰: '护卫舰',
      航空母舰: '航空母舰',
      护卫艇: '护卫艇',
      巡逻艇: '巡逻艇',
      登陆舰: '登陆舰',
      补给舰: '补给舰',
      防空导弹: '防空导弹',
      反舰导弹: '反舰导弹',
      反潜导弹: '反潜导弹',
      巡航导弹: '巡航导弹',
      弹道导弹: '弹道导弹',
      鱼雷: '鱼雷',
    };
    return categoryNames[category] || category;
  };

  /**
   * 清空错误信息
   */
  const clearError = () => {
    error.value = null;
  };

  return {
    // 状态
    currentScenario,
    modelList,
    treeData,
    loading,
    error,

    // 计算属性
    modelsByType,
    modelsByCategory,
    modelCount,

    // 方法
    setCurrentScenario,
    fetchScenarioModels,
    setModelList,
    addModel,
    removeModel,
    updateModel,
    getModelById,
    getModelsByType,
    getModelsByCategory,
    convertModelsToTreeData,
    clearAll,
    clearError,
  };
});
