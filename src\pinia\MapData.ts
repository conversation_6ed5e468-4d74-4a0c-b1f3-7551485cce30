/*
 * <AUTHOR> 老范
 * @Date         : 2022-08-19 17:27:50
 * @LastEditors: 老范
 * @LastEditTime: 2023-05-10 16:47:12
 * @Description  : 请填写简介
 */

import {defineStore} from 'pinia';
import type {GeoJsonType} from '@/types/index';

interface MapState {
  geojson: GeoJsonType;
  stations: GeoJsonType;
  clickPoint: any; // 点击高亮框
  positionTree: any[]; // ZD实体树
  troops: any[]; // BD实体树
  positionEntityId: number;
  coachTree: any[]; // 车厢实体树
  trainIdAry: any[]; // 车厢实体树
}

const defaultState: MapState = {
  geojson: {
    type: 'geojson',
    data: {
      type: 'FeatureCollection',
      features: [],
    },
  },
  stations: {
    type: 'geojson',
    data: {
      type: 'FeatureCollection',
      features: [],
    },
  },
  // 实体高亮
  clickPoint: {
    type: 'geojson',
    data: {
      type: 'FeatureCollection',
      features: [],
    },
  },
  // 部署详情树
  positionTree: [],
  // 部署详情树
  troops: [],
  coachTree: [],
  //  记录实体id
  positionEntityId: 0,
  trainIdAry: [],
};

export const useMapState = defineStore('mapData', {
  state: () => {
    return defaultState;
  },
  getters: {},
  actions: {},
});
