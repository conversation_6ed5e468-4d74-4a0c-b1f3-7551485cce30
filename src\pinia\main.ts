/*
 * @Author: 老范
 * @Date: 2023-05-10 15:02:19
 * @LastEditors: 老范
 * @LastEditTime: 2023-10-26 16:35:49
 * @Description: 请填写简介
 */
/*
 * @Author: ylx <EMAIL>
 * @Date: 2022-10-28 16:09:02
 * @LastEditors: ylx <EMAIL>
 * @LastEditTime: 2022-10-28 21:07:25
 * @FilePath: \qianduan\src\pinia\main.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import {defineStore} from 'pinia';
import simulation from '@/views/simulation/simulation.vue';
import {markRaw} from 'vue';

interface State {
  tagsList: any[];
  collapse: boolean;
  comName: any;
}

const defaultState: State = {
  tagsList: [],
  collapse: false,
  comName: markRaw(simulation),
};

export const mainStore = defineStore('main', {
  state: () => {
    return defaultState;
  },
  getters: {},
  actions: {
    // 侧边栏折叠
    handleCollapse(data: boolean) {
      this.collapse = data;
    },
    clearTags() {
      defaultState.tagsList = [];
    },
  },
});
