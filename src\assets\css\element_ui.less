@import url('../css/main.css');
@font-face {
  font-family: Medium;
  src: url('../medium.OTF');
}
@font-face {
  font-family: Regular;
  src: url('../regular.OTF');
}
@font-face {
  font-family: Normal;
  src: url('../normal.OTF');
}

:root {
  --primary-color: #02e0ff;
  --active-bg-color: #1f334a;
  --select-hover-color: #0a79b8;
  --select-bg: #00598c;
  --base-bg-color: #0c1d36;
  --input-border-color: #4a86d2;
  --input-background: transparent;
  --table-selected-bg-color: #31495e;
  --bg-dark: #040b22;
  --title-font-color: #c3f8ff;
  --border-rdius-5: 5px;
  --font-size-small: 14px;
  --font-size-big: 18px;
  --icon-size-small: 20px;
  --icon-size-big: 30px;
  --font-color-white: white;
  --font-color: #c3f8ff;
  --lightcyan: #02e0ff;
  --box-bg-color: rgb(16 37 63 / 100%);
  --dark-green: rgb(0 255 255 / 30%);
  --dark-grey: rgb(255 255 255 / 25%);
  --translucent-grey: rgb(255 255 255 / 50%);
  --light-grey: rgb(255 255 255 / 80%);
  --left-distance: 70px;
  --card-border-color: #96c4e4;
  --el-table-text-color: #fff;
  --el-table-header-text-color: #fff;
  --el-table-bg-color: transparent;
  --el-table-tr-bg-color: transparent;
  --el-table-border: 1px solid transparent;
  --el-table-tr-hover-bg: #3c75c3;
  --el-dialog-header-bg: #12577f;
  --el-dialog-border-color: #36b6ff;
  --el-dialog-body-bg: #022e48;
  --expand-icon-color: #5fc5ff;
  --hover-bg: #0075b8a6;
}

* {
  padding: 0;
  margin: 0;
  line-height: 1;
  box-sizing: border-box;
}

html,
body,
#app {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  overflow: hidden;
  letter-spacing: 1px;
  color: var(--font-color-white);
  background-color: rgb(0 0 0 / 60%);
}

.el-dialog__body {
  padding: 5px 10px !important;
  color: #343131;
  // color: var(--font-color-white);
  background: var(--el-dialog-body-bg);

  .dialog-footer {
    .el-button {
      height: 30px;
      font-size: 16px;
    }

    span {
      padding: 0 15px;
    }
  }
}

.el-dialog__footer {
  border-bottom-left-radius: var(--border-rdius-5);
  border-bottom-right-radius: var(--border-rdius-5);
}

.el-dialog__header {
  position: relative;
  display: flex;
  height: 32px;
  padding: 0;
  margin-right: 0;
  font-family: Medium;
  background: var(--el-dialog-header-bg);
  justify-content: center;

  .el-dialog__title {
    font-size: 16px;
    line-height: 32px;
    letter-spacing: 2px;
    color: var(--font-color-white);
  }
}

.el-dialog__header.show-close {
  padding: 0;
}

.el-dialog {
  --el-dialog-content-font-size: unset;

  padding: unset;
  color: var(--font-color-white);
  background: transparent;
  border: 1px solid var(--el-dialog-border-color);
}

.el-button {
  height: 27px;
  /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
  font-family: Regular;
  font-size: 14px;
  color: var(--font-color-white);
  background: #125d8d;
  border: solid 1px var(--el-dialog-border-color);
  border-radius: 2px;
}

.el-button.is-disabled,
.el-button.is-disabled:hover {
  background: #014a77;
  border-color: #1a7ab2;

  /* stylelint-disable-next-line no-descending-specificity */
  span {
    color: #a8abb2;
  }
}

/* stylelint-disable-next-line no-descending-specificity */
.el-button:hover {
  background: #125d8d;
  border-color: #c6e2ff;
  /* stylelint-disable-next-line no-descending-specificity */
  span,
  .el-icon {
    color: var(--font-color-white);
  }
}

/* stylelint-disable-next-line no-descending-specificity */
button:focus,
button:focus-visible {
  outline: unset;
}

.el-button:active {
  color: var(--font-color-white);
  background: #125d8d;
}
// 输入框placeholder字体大小
.el-input {
  --el-component-size-large: 40px;
  --el-component-size: 30px;
  --el-component-size-small: 23px;

  background-image: linear-gradient(0deg, #0c5e8e 0%, #00456d 75%);

  .el-input__wrapper {
    background: var(--input-background) !important;
    border: solid 1px #1c8fd0;
    box-shadow: none !important;
    border-radius: 0;
  }

  input[type='number'] {
    padding-left: 0;
    appearance: textfield;
    line-height: 1px !important;
  }

  input[type='number']::-webkit-inner-spin-button,
  input[type='number']::-webkit-outer-spin-button {
    appearance: none;
    margin: 0;
  }
}

.el-input-group__append {
  padding: 0 5px;
  color: var(--font-color-white);
  background-color: unset;
}

.el-input.is-disabled {
  background-image: linear-gradient(0deg, #4e6f82 -20%, #05344e 100%);

  .el-input__wrapper {
    border: 1px solid #8eaec5;
    border-bottom-color: #dbf1fc;
  }
}

.el-form-item {
  margin-bottom: 15px;
}

.el-form-item__label {
  font-family: Normal;
  font-size: 16px;
  line-height: 34px;
  color: var(--font-color-white);
}

.el-input__inner {
  font-size: var(--font-size-small);
  color: var(--font-color-white);
  background: var(--input-background);
  border: 0;
  box-shadow: none !important;
}

.el-textarea,
.el-textarea.is-disabled {
  .el-textarea__inner {
    height: 100%;
    color: var(--font-color-white);
    background: var(--input-background);
    box-shadow: none !important;
    border: 1px solid var(--input-border-color);
    border-radius: 2px;
  }
}

.el-select {
  .el-select__wrapper {
    box-shadow: 0 0 0 1px #1c8fd0 inset;
    background-image: linear-gradient(0deg, #0c5e8e 0%, #00456d 75%);
    border-radius: 0;

    .el-select__selected-item {
      span {
        line-height: 2;
        color: var(--font-color-white);
      }
    }
  }

  .el-select__wrapper.is-hovering:not(.is-focused) {
    box-shadow: 0 0 0 1px var(--primary-color) inset;
  }

  .el-select__tags {
    position: absolute;
    top: 0;
    left: 4px;
    z-index: 1;

    .el-tag {
      --el-tag-hover-color: unset;

      background: #1287cc;
      border: unset;

      .el-tag__content {
        .el-select__tags-text {
          font-family: auto;
          color: var(--font-color-white);
        }
      }

      .el-tag__close {
        color: var(--font-color-white);
      }
    }
  }
}

.el-select__popper.el-popper[role='tooltip'] {
  background: var(--select-bg) !important;
  border: 1px solid var(--el-dialog-border-color);
}

.el-select-dropdown__item {
  color: var(--font-color-white);
}

.el-select-dropdown__item.is-selected {
  color: var(--el-dialog-border-color);
}

.el-select__caret {
  font-size: 18px;
  color: var(--expand-icon-color);
}

.el-select-dropdown__item:hover {
  background-color: var(--select-hover-color);
}

.el-select-dropdown__item.selected {
  background-color: var(--select-hover-color);
}

.el-select-dropdown__item.is-hovering {
  background-color: var(--select-hover-color);
}

.el-select .el-input__inner {
  color: var(--primary-color);
  border: 0;
}

.el-select .el-input .el-select__caret {
  color: var(--primary-color);
}

.el-select__placeholder.is-transparent {
  /* stylelint-disable-next-line no-descending-specificity */
  span {
    color: #a8abb2 !important;
  }
}

.el-tabs__nav-wrap::after {
  background-color: var(--active-bg-color);
}

.el-tabs__item {
  padding: 0 8%;
  color: var(--font-color-white);
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 4px;
  height: 8px;
}
// 滚动条
::-webkit-scrollbar-thumb {
  width: 10px;
  height: 30px;
  background-color: #097cbc;
  border-radius: var(--border-rdius-5);
}

.el-tree {
  height: 100%;
  color: var(--font-color-white);
  background-color: transparent;
  border-radius: var(--border-rdius-5);
}

.el-tree-node__content {
  height: unset;
}

.el-tree-node__content:hover {
  background-color: #00a2ff66;
}

.el-tree-node:focus > .el-tree-node__content {
  background-color: transparent;
}

.el-tree-node__expand-icon {
  font-size: 18px;
  color: var(--expand-icon-color);
}

.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #00a2ffa6;
}

.el-notification.right {
  color: var(--font-color-white);
  background-color: var(--select-hover-color);
  border: none;
}

.el-notification__content p {
  color: var(--font-color-white);
}

.el-notification__title {
  color: var(--font-color-white);
}

.el-radio {
  height: 30px;
  font-size: var(--font-size-small);
  color: var(--font-color-white);
}

.el-message-box {
  background: var(--el-dialog-body-bg);
  border: 1px solid var(--el-dialog-border-color);
}

.el-message-box__message p {
  color: var(--font-color-white);
}

.el-message-box__title {
  color: var(--font-color-white);
}

.el-table {
  --el-table-header-bg-color: transparent;
  --el-table-bg-color: transparent;
  --el-table-tr-bg-color: transparent;
  --el-table-border: 1px solid transparent;
  --el-table-text-color: var(--font-color-white);

  .el-table__cell {
    padding: 0;
    background: transparent;
  }

  thead th {
    font-family: Medium;
    font-weight: unset;
  }

  .el-table__header {
    .el-table__cell {
      height: 40px;
      padding: 8px 0;

      .cell {
        font-size: 16px;
        color: #ace1ff;
      }
    }
  }

  .el-table__body {
    // border-collapse: separate;
    // border-spacing: 0 10px;
    width: unset !important;

    .el-table__row {
      font-size: 15px;
      background: #022e48;

      .el-table__cell {
        height: 40px;
        padding: 5px 0;

        .cell {
          .el-table__placeholder {
            width: unset;
            padding-left: 16px;
          }
        }
      }
    }
  }
}

.el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background: #053957;

  .el-table__cell {
    height: 40px;
    padding: 5px 0;
  }
}

.el-table--enable-row-hover .el-table__body {
  .current-row {
    td {
      background: var(--hover-bg) !important;
    }
  }
}

.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background-color: var(--hover-bg);
}

/* stylelint-disable-next-line no-descending-specificity */
.el-table--enable-row-hover .el-table__body .current-row > td.el-table__cell {
  background-color: #3c75c3;
}

.el-table--enable-row-hover .el-table__body .deday-edit-color {
  background: #30465c;
}

.el-table--enable-row-hover .el-table__body .custom-color td {
  background: #043270 !important;
}

.el-table__inner-wrapper::before {
  content: none;
}

.el-table__expand-icon {
  height: 14px;

  .el-icon {
    scale: 1.5;
    color: #fff;
  }
}

.el-dialog__headerbtn {
  top: 50%;
  display: flex;
  width: 30px;
  height: auto;
  font-size: 20px;
  color: #37b8ff;
  transform: translateY(-50%);
  justify-content: center;
}

.el-pager .more::before {
  color: var(--font-color-white);
}
// 进度条居中
.el-progress-bar__inner {
  line-height: 0;
}
// 输入框选中高亮
.el-input .is-focus,
.el-textarea__inner:focus {
  border-color: var(--primary-color) !important;
}

.el-popconfirm .el-popper__arrow::before {
  background-color: var(--primary-color);
}
// 图标大小
.iconFontSize {
  font-size: var(--icon-size-big) !important;
  cursor: pointer;
}

.iconfont-size-small {
  font-size: var(--icon-size-small) !important;
  cursor: pointer;
}

.checkboxFontColorWhte {
  color: var(--font-color-white);
}

::-webkit-scrollbar-corner {
  background-color: var(--base-bg-color) !important;
}
// 动画
.bounce {
  animation-name: bounce;
}

.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.animated.infinite {
  animation-iteration-count: infinite;
}
@keyframes bounce {
  0%,
  100% {
    transform: translateX(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-10px);
  }

  20%,
  40%,
  60%,
  80% {
    transform: translateX(10px);
  }
}
// loading背景色
.el-loading-mask {
  background-color: var(--base-bg-color);
  opacity: 0.5;
}

// 下拉框倒三角样式
.el-select__popper.el-popper[role='tooltip'][data-popper-placement^='top'] .el-popper__arrow::before,
.el-dropdown__popper.el-popper[role='tooltip'][data-popper-placement^='bottom'] .el-popper__arrow::before,
.el-select__popper.el-popper[role='tooltip'][data-popper-placement^='bottom'] .el-popper__arrow::before {
  background-color: var(--select-bg) !important;
}

.el-dropdown__popper.el-popper[role='tooltip'] {
  border: 1px solid var(--primary-color);
}

.el-card {
  color: var(--font-color-white);
  background-color: transparent;
  border: 1px solid var(--card-border-color);
}

.el-card .el-card__header {
  border-bottom: 1px solid var(--card-border-color);
}

.el-dropdown__popper {
  // top: 37px !important;
  background: #003a5d !important;
  border: solid 1px #36b6ff !important;
  border-radius: 2px;

  /* stylelint-disable-next-line no-descending-specificity */
  .el-popper__arrow::before {
    background-color: #003a5d !important;
  }

  .el-scrollbar {
    .el-dropdown-menu__item {
      max-width: 160px;
      // min-width: 135px;
      padding: 5px;
      font-family: Medium;
      font-size: 16px;
      color: #a2ddff;
      border-bottom: 1px solid #0a5784;
      justify-content: center;
      text-align: center;
    }

    .el-dropdown-menu__item:focus,
    .el-dropdown-menu__item:not(.is-disabled):hover {
      // color: var(--primary-color);
      background-color: #085e91;
    }
  }
}

.el-select-dropdown__empty {
  color: var(--font-color-white);
  background-color: linear-gradient(0deg, #0c5e8e 0%, #00456d 75%);
}

// select样式
.el-select--mini {
  width: 100%;
}

.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: var(--select-hover-color);
}
// tab样式
.el-tabs__content {
  height: calc(100% - 60px);
}

.el-cascader__dropdown {
  background-image: linear-gradient(0deg, #0c5e8e 0%, #00456d 75%) !important;
}

.el-cascader-node__label {
  color: white;
}

.el-cascader-panel {
  --el-cascader-node-background-hover: var(--select-hover-color);
}

.el-cascader__tags .el-tag:not(.is-hit) {
  background-color: var(--select-hover-color);
}

.el-popup-parent--hidden {
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

.el-upload-list {
  width: 200px;
  overflow: auto;
}

.el-cascader-menu {
  border-right: 1px solid var(--primary-color);
}

.el-cascader:not(.is-disabled):hover .el-input__inner {
  border-color: var(--primary-color) !important;
}

.el-cascader .el-input .el-icon-arrow-down {
  color: var(--primary-color);
}

.el-cascader-node__postfix {
  color: var(--primary-color);
}

.el-popper.is-light {
  max-width: 500px;
  color: var(--font-color-white);
  background: #004368;
  border: 1px solid #36b6ff;

  /* stylelint-disable-next-line no-descending-specificity */
  .el-popper__arrow::before {
    background-color: #003a5d !important;
  }
}

.cursor-pointer {
  cursor: pointer;
}

.maplibregl-ctrl {
  display: none !important;
}

.maplibregl-canvas {
  border-radius: 10px !important;
}

.el-checkbox {
  color: var(--font-color-white);
}

.el-checkbox__inner {
  width: 16px;
  height: 16px;
}

.el-checkbox__label {
  font-size: 16px;
}

.el-checkbox__inner::after {
  left: 5px;
  height: 8px;
}

/* -----------------------------el-switch样式 ------------------------- */
.el-switch__core {
  background-color: #0c578393;
  border-color: var(--border-color);
}

.el-switch__label {
  font-family: Medium;
  color: #fff;
  line-height: 20px;
}

.el-switch__label.is-active {
  font-size: 15px;
  color: #21aeff;
}

.el-switch__core .el-switch__action {
  background-color: #21aeff;
}

.el-switch.is-checked .el-switch__core {
  border-color: var(--border-color);
  background-color: #0c578393;
}
// 抽屉样式
.el-drawer {
  --el-drawer-bg-color: #022e48;

  .el-drawer__header {
    padding: 10px;
    margin-bottom: 0;
    color: var(--font-color);
    /* stylelint-disable-next-line order/properties-order */
    font-family: Regular;
  }

  border: 1px solid var(--el-dialog-border-color);

  .el-drawer__body {
    padding: 0;
  }
}

.el-table-hide-row {
  display: none;
}

.block-param-row {
  background: #006199 !important;
}

.el-collapse-item__arrow {
  display: none;
}

.el-pagination {
  --el-pagination-button-width: 24px;
  --el-pagination-button-height: 24px;
  --el-pagination-button-bg-color: #043755;
  --el-pagination-button-disabled-bg-color: #043755;

  .btn-prev,
  .btn-next {
    color: var(--font-color-white);
    background-color: unset;
    border: solid 1px var(--el-dialog-border-color);
  }

  button:disabled {
    background: var(--el-pagination-button-bg-color) !important;
  }

  .el-pager {
    li {
      margin: 0 4px;
      font-family: Normal;
      color: var(--font-color-white);
      background-color: unset;
      border: solid 1px var(--el-dialog-border-color);
    }

    li.is-active {
      font-weight: unset;
      background: #145d89 !important;
    }
  }

  .el-pagination__total {
    color: var(--font-color-white);
  }
}

.el-scrollbar {
  --el-scrollbar-opacity: 1;
  --el-scrollbar-bg-color: #097cbc;
  --el-scrollbar-hover-opacity: 1;
  --el-scrollbar-hover-bg-color: #097cbc;
}

.overflow_tooltip {
  display: none;
}

// TODO
.el-tag {
  --el-tag-hover-color: unset;

  background: #1287cc;
  border: unset;

  .el-tag__content {
    .el-select__tags-text {
      font-family: auto;
      color: var(--font-color-white);
    }
  }

  .el-tag__close {
    color: var(--font-color-white);
  }
}
