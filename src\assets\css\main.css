@import url('./timescale.css');
@import url('./font.css');
@import url('./flex.css');
@import url('./margin.css');
@import url('./padding.css');
@font-face {
  font-family: Medium;
  src: url('../medium.OTF');
}
@font-face {
  font-family: Regular;
  src: url('../regular.OTF');
}
@font-face {
  font-family: Normal;
  src: url('../normal.OTF');
}

/* 全局样式设置，element覆盖，公共样式书写 */
:root {
  --selected-font-color: #36b6ff;
  --title-color: #fff;
  --font-color: #92d7ff;
  --icon-color: #5fc5ff;
  --border-color: #32aff7;
  --title-bg-color: #12577f;
  --dialog-bg-color: #022e48;
  --panel-bg-color: #002f49;
  --split-line-color: #3186b3;
  --highlight-color: #006ca9;
  --th-bg-color: #14628f;
  --mask-bg: rgb(0 0 0 / 60%);
  --model-list-left-distance: 70px;
  --nav-left-distance: 0;
  --draw-left-distance: 465px;
  --border: 1px solid var(--border-color);
  --el-border-color: #415c9b !important;

  /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
  font-family: Regular;
}

.pointer {
  cursor: pointer;
}

.overflow_hidden {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text_align_c {
  text-align: center;
}

.height_100 {
  height: 100%;
}

.width_100 {
  width: 100%;
}

.p-r {
  position: relative;
}

.p-a {
  position: absolute;
}

.sim_light {
  background: url('/images/sim_light.png') no-repeat 0 0 / 100% 100% !important;
}

.sim_dark {
  background: url('/images/sim_dark.png') no-repeat 0 0 / 100% 100% !important;
}

.back_dark {
  background: url('/images/back_dark.png') no-repeat 0 0 / 100% 100% !important;
}

.back_light {
  background: url('/images/back_light.png') no-repeat 0 0 / 100% 100% !important;
}

.init {
  background: url('/images/init_dark.png') no-repeat;
  background-size: 100% 100%;
}

.begin {
  background: url('/images/start_dark.png') no-repeat;
  background-size: 100% 100%;
}

.pause {
  background: url('/images/pause_dark.png') no-repeat;
  background-size: 100% 100%;
}

.continue {
  background: url('/images/continue_dark.png') no-repeat;
  background-size: 100% 100%;
}

.finish {
  background: url('/images/stop_dark.png') no-repeat;
  background-size: 100% 100%;
}

.speed {
  background: url('/images/speed_dark.png') no-repeat;
  background-size: 100% 100%;
}

.jump {
  background: url('/images/jump_dark.png') no-repeat;
  background-size: 100% 100%;
}

.push {
  background: url('/images/push_dark.png') no-repeat;
  background-size: 100% 100%;
}

.init_light {
  background: url('/images/init_light.png') no-repeat;
  cursor: pointer;
  background-size: 100% 100%;
}

.begin_light {
  background: url('/images/start_light.png') no-repeat;
  cursor: pointer;
  background-size: 100% 100%;
}

.pause_light {
  background: url('/images/pause_light.png') no-repeat;
  cursor: pointer;
  background-size: 100% 100%;
}

.continue_light {
  background: url('/images/continue_light.png') no-repeat;
  cursor: pointer;
  background-size: 100% 100%;
}

.finish_light {
  background: url('/images/stop_light.png') no-repeat;
  cursor: pointer;
  background-size: 100% 100%;
}

.speed_light {
  background: url('/images/speed_light.png') no-repeat;
  cursor: pointer;
  background-size: 100% 100%;
}

.jump_light {
  background: url('/images/jump_light.png') no-repeat;
  cursor: pointer;
  background-size: 100% 100%;
}

.push_light {
  background: url('/images/push_light.png') no-repeat;
  cursor: pointer;
  background-size: 100% 100%;
}

.divider_public {
  background: url('/images/divider_bg.png') no-repeat 0 0 / 100% 100%;
}

.disabled {
  opacity: 0.5;

  span {
    cursor: not-allowed !important;
  }
}

:deep(.el-textarea__inner) {
  height: 159px !important;
}

