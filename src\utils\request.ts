/*
 * @Author: 老范
 * @Date: 2023-05-09 11:06:35
 * @LastEditors: liukun
 * @LastEditTime: 2024-11-04 19:50:00
 * @Description: 请填写简介
 */

import axios, {AxiosInstance, AxiosRequestConfig} from 'axios'; // 引入axios
import {ElMessage} from 'element-plus';
import type {MyResponseType} from '@/types/index';
interface serviceConfigType extends AxiosInstance {
  urlType: string;
}
// type serviceConfigType = {
//   urlType: string;
// } & AxiosRequestConfig;
const {BaseResourceUrl, ControlUrl3D} = globalConfig;
const service: any = axios.create({
  baseURL: BaseResourceUrl,
  timeout: 99999,
});

// http request 拦截器
service.interceptors.request.use(
  (config: AxiosRequestConfig | any) => {
    config.headers = {
      'Content-Type': 'application/json',
      // 'x-token': userStore.token,
      ...config.headers,
    };

    switch (config.urlType) {
      // 如果项目需拓展多个后端地址,添加case,并在public/config/config.js中添加配置项
      case 'control':
        config.url = ControlUrl3D + config.url;
        break;
      // 默认后端环境
      default:
        config.url = BaseResourceUrl + config.url;
        break;
    }
    return config;
  },
  (error: any) => {
    ElMessage({
      showClose: true,
      message: error,
      type: 'error',
    });
    return error;
  }
);
const successCodeAry: number[] = [200, 201, 202, 204];
const errorCodeAry: number[] = [400, 401, 403, 404, 406, 410, 422, 500];
// http response 拦截器
service.interceptors.response.use(
  (response: MyResponseType) => {
    if (successCodeAry.includes(response.status) && !errorCodeAry.includes(response.data.code)) return response.data;
    ElMessage({
      showClose: true,
      message: response.data.msg,
      type: 'error',
    });
    if (response.data.code === 401)
      window.top?.postMessage(
        {
          tokenFailure: true,
        },
        '*'
      );
    return Promise.reject(response.data.msg);
  },
  (error: any) => {
    if (errorCodeAry.includes(error.response.status))
      ElMessage({
        showClose: true,
        message: error.response.data,
        type: 'error',
      });
    if (error.response.status === 401)
      window.top?.postMessage(
        {
          tokenFailure: true,
        },
        '*'
      );
    return Promise.reject(error.response.data);
  }
);
export default service;
