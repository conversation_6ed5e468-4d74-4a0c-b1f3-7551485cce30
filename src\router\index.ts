/*
 * <AUTHOR> 老范
 * @Date         : 2022-08-09 09:30:45
 * @LastEditors: liukun
 * @LastEditTime: 2025-02-11 14:38:49
 * @Description  : 请填写简介
 */
import {createRouter, createWebHistory} from 'vue-router';

const routes = [
  {
    path: '/',
    name: '',
    redirect: 'Unity',
  },
  {
    path: '/Unity',
    name: 'Unity',
    component: () => import('@/views/simulation/simulation_new.vue'),
  },
];
// window.sessionStorage.getItem('token')
const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
