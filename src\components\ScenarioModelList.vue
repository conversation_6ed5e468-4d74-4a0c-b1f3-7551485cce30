<!--
 * @Description: 想定模型列表组件
 * @Author: AI Assistant
 * @Date: 2025-06-20
 * @LastEditors: 老范
 * @LastEditTime: 2025-07-08 10:02:14
-->
<template>
  <div class="scenario-model-list">
    <div class="header">
      <h3>想定模型列表</h3>
      <div class="model-count">
        <span>总计: {{ scenarioStore.modelCount }} 个模型</span>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="scenarioStore.loading" class="loading">
      <el-loading text="加载中..." />
    </div>

    <!-- 错误信息 -->
    <div v-if="scenarioStore.error" class="error">
      <el-alert :title="scenarioStore.error" type="error" show-icon :closable="false" />
    </div>

    <!-- 模型列表 -->
    <div v-if="!scenarioStore.loading && !scenarioStore.error" class="model-content">
      <!-- 按类型分组显示 -->
      <div v-if="groupBy === 'type'" class="group-container">
        <div v-for="(models, type) in scenarioStore.modelsByType" :key="type" class="model-group">
          <div class="group-header">
            <h4>{{ type }} ({{ models.length }})</h4>
          </div>
          <div class="model-items">
            <div v-for="model in models" :key="model.id" class="model-item" :class="{active: selectedModel?.id === model.id}" @click="selectModel(model)">
              <div class="model-info">
                <div class="model-name">{{ model.name }}</div>
                <div class="model-description">{{ model.description || '无描述' }}</div>
              </div>
              <div class="model-actions">
                <el-button size="small" type="primary" @click.stop="viewModel(model)"> 查看 </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 按类别分组显示 -->
      <div v-else-if="groupBy === 'category'" class="group-container">
        <div v-for="(models, category) in scenarioStore.modelsByCategory" :key="category" class="model-group">
          <div class="group-header">
            <h4>{{ category }} ({{ models.length }})</h4>
          </div>
          <div class="model-items">
            <div v-for="model in models" :key="model.id" class="model-item" :class="{active: selectedModel?.id === model.id}" @click="selectModel(model)">
              <div class="model-info">
                <div class="model-name">{{ model.name }}</div>
                <div class="model-type">类型: {{ model.type }}</div>
                <div class="model-description">{{ model.description || '无描述' }}</div>
              </div>
              <div class="model-actions">
                <el-button size="small" type="primary" @click.stop="viewModel(model)"> 查看 </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 列表显示 -->
      <div v-else class="list-container">
        <div v-for="model in scenarioStore.modelList" :key="model.id" class="model-item" :class="{active: selectedModel?.id === model.id}" @click="selectModel(model)">
          <div class="model-info">
            <div class="model-name">{{ model.name }}</div>
            <div class="model-meta">
              <span class="model-type">{{ model.type }}</span>
              <span v-if="model.category" class="model-category">{{ model.category }}</span>
            </div>
            <div class="model-description">{{ model.description || '无描述' }}</div>
          </div>
          <div class="model-actions">
            <el-button size="small" type="primary" @click.stop="viewModel(model)"> 查看 </el-button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="scenarioStore.modelCount === 0" class="empty-state">
        <el-empty description="暂无模型数据" />
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="controls">
      <div class="group-controls">
        <span>分组方式:</span>
        <el-radio-group v-model="groupBy" size="small">
          <el-radio-button label="list">列表</el-radio-button>
          <el-radio-button label="type">按类型</el-radio-button>
          <el-radio-button label="category">按类别</el-radio-button>
        </el-radio-group>
      </div>
      <div class="action-controls">
        <el-button size="small" @click="refreshModels">刷新</el-button>
        <el-button size="small" type="primary" @click="exportModels">导出</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref} from 'vue';
import {ElMessage} from 'element-plus';
import {useScenarioStore} from '@/pinia/scenario';
import type {ScenarioModelType} from '@/types/index';

// 使用scenario store
const scenarioStore = useScenarioStore();

// 组件状态
const groupBy = ref<'list' | 'type' | 'category'>('type');
const selectedModel = ref<ScenarioModelType | null>(null);

// 事件定义
const emits = defineEmits<{
  modelSelected: [model: ScenarioModelType];
  modelViewed: [model: ScenarioModelType];
}>();

/**
 * 选择模型
 */
const selectModel = (model: ScenarioModelType) => {
  selectedModel.value = model;
  emits('modelSelected', model);
};

/**
 * 查看模型详情
 */
const viewModel = (model: ScenarioModelType) => {
  emits('modelViewed', model);
};

/**
 * 刷新模型列表
 */
const refreshModels = async () => {
  if (scenarioStore.currentScenario?.id) {
    try {
      await scenarioStore.fetchScenarioModels(scenarioStore.currentScenario.id);
      ElMessage.success('模型列表刷新成功');
    } catch (error) {
      ElMessage.error('刷新失败');
    }
  } else {
    ElMessage.warning('请先选择想定');
  }
};

/**
 * 导出模型列表
 */
const exportModels = () => {
  if (scenarioStore.modelCount === 0) {
    ElMessage.warning('暂无模型数据可导出');
    return;
  }

  const data = JSON.stringify(scenarioStore.modelList, null, 2);
  const blob = new Blob([data], {type: 'application/json'});
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `scenario_models_${Date.now()}.json`;
  a.click();
  URL.revokeObjectURL(url);

  ElMessage.success('模型列表导出成功');
};
</script>

<style scoped>
.scenario-model-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.model-count {
  font-size: 14px;
  color: #666;
}

.loading,
.error {
  padding: 20px;
}

.model-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.model-group {
  margin-bottom: 24px;
}

.group-header {
  margin-bottom: 12px;
}

.group-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  padding: 8px 12px;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.model-items,
.list-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.model-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.model-item:hover {
  border-color: #2196f3;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
}

.model-item.active {
  border-color: #2196f3;
  background-color: #e3f2fd;
}

.model-info {
  flex: 1;
}

.model-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.model-meta {
  display: flex;
  gap: 12px;
  margin-bottom: 4px;
}

.model-type,
.model-category {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  background-color: #f0f0f0;
  color: #666;
}

.model-description {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

.controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.group-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.action-controls {
  display: flex;
  gap: 8px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
