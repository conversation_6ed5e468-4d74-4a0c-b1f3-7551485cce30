<!--
 * @Description: 回放列表
 * @Author: tzs
 * @Date: 2024-09-11 11:46:03
 * @LastEditors: liukun
 * @LastEditTime: 2025-01-22 10:45:19
-->
<script setup lang="ts">
import {ref, watch, nextTick} from 'vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import {getListApi, getPlaybackListApi, exportPlaybackApi, deletePlaybackApi} from '@/api/simulation';
import {ScenarioType, PlayBackType} from '@/types/index';
const emits = defineEmits(['receiveData', 'confimInit']);

const list = ref<ScenarioType[]>([]);
const playbackList = ref<PlayBackType[]>([]);
// 分页总数
const total = ref(0);
const dialogVisible = ref(false);
// 选中某一行的数据
const playbackData = ref<PlayBackType>({} as PlayBackType);
const scenarioData = ref<ScenarioType>({} as ScenarioType);
const scenarioTableRef = ref();
const playbackTableRef = ref();
// 获取想定列表的参数
const listQuery = ref({
  s: '',
  pageIndex: 1,
  pageSize: 10,
});

watch(
  () => dialogVisible.value,
  async newV => {
    if (!newV) return;
    await getList();
  }
);
watch(
  () => scenarioData.value?.id,
  () => {
    playbackData.value = {} as PlayBackType;
  }
);
const getList = async () => {
  const res = (await getListApi(listQuery.value)).data;
  list.value = res.list;
  total.value = res.count;
  nextTick(() => {
    if (!scenarioData.value?.id) return;
    const row = list.value.find(i => i.id === scenarioData.value.id);
    if (row) {
      scenarioTableRef.value.setCurrentRow(row);
    } else {
      scenarioData.value = {} as ScenarioType;
      playbackList.value = [];
      playbackData.value = {} as PlayBackType;
    }
  });
  return;
};
// 选择表格某一行数据
const selectScenarioRow = async (row: ScenarioType) => {
  if (!row) return;
  const res = await getPlaybackListApi(row.id);
  playbackList.value = res.data.list || [];
  scenarioData.value = row;
  nextTick(() => {
    if (!playbackData.value?.id) return;
    const playbackRow = playbackList.value.find(i => i.id === playbackData.value.id);
    playbackRow && playbackTableRef.value.setCurrentRow(playbackRow);
  });
};
const selectPlaybackRow = (row: PlayBackType) => {
  if (!row) return;
  playbackData.value = row;
};
// 确认选中想定
const confim = () => {
  if (!playbackData.value?.id) return ElMessage.error('请选择回放数据');
  emits('receiveData', playbackData.value);
  dialogVisible.value = false;
};

const handleCurrentChange = (val: number) => {
  listQuery.value.pageIndex = val;
  scenarioData.value = {} as ScenarioType;
  playbackList.value = [];
  playbackData.value = {} as PlayBackType;
  getList();
};
const searchList = async () => {
  listQuery.value.pageIndex = 1;
  await getList();
};
// 导出回放
const exportPlayback = (tableName: string, type: string) => {
  window.location.href = exportPlaybackApi(tableName, type);
};
// 删除回放记录
const deletePlayback = async (row: {id: number; scenarioId: number}) => {
  ElMessageBox.confirm('确定要删除吗?', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await deletePlaybackApi({ids: [row.id]});
    ElMessage.success('删除成功');
    const {data} = await getPlaybackListApi(row.scenarioId);
    playbackList.value = data.list;
  });
};
// 双击
const handleDblTableRow = () => {
  ElMessageBox.confirm('是否初始化回放场景?', '回放初始化', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    emits('confimInit', playbackData.value);
    dialogVisible.value = false;
  });
};
defineExpose({
  dialogVisible,
});
</script>

<template>
  <el-dialog v-model="dialogVisible" title="回放列表" width="1080px" draggable :close-on-click-modal="false" :close-on-press-escape="false" destroy-on-close :header-row-style="{background: '#084669;'}" align-center>
    <div class="search-box">
      <el-input v-model="listQuery.s" clearable placeholder="请输入ID/名称" @keyup.enter="searchList" @clear="searchList">
        <template #suffix>
          <el-icon class="pointer"><Search @click.stop="searchList" /></el-icon>
        </template>
      </el-input>
    </div>
    <el-table ref="scenarioTableRef" :data="list" :show-overflow-tooltip="true" stripe :height="300" highlight-current-row :row-style="{height: '20px'}" @current-change="selectScenarioRow">
      <el-table-column label="想定ID" prop="id" align="center" width="98"> </el-table-column>
      <el-table-column label="仿真想定名称" prop="name" align="center" width="230"></el-table-column>
      <el-table-column label="仿真想定描述" prop="description" align="center" width="233"> </el-table-column>
      <el-table-column label="创建时间" prop="createTime" align="center"> </el-table-column>
      <el-table-column label="更新时间" prop="updateTime" align="center"> </el-table-column>
    </el-table>
    <div class="width_100 dis-flex flex-x-center pad_v_10">
      <el-pagination :current-page="listQuery.pageIndex" :page-size="listQuery.pageSize" layout=" prev, pager, next" :total="total" @current-change="handleCurrentChange"></el-pagination>
    </div>
    <div class="playback-list">
      <div class="title">回放管理</div>
      <div class="content">
        <el-table ref="playbackTableRef" :data="playbackList" :show-overflow-tooltip="true" stripe :height="300" highlight-current-row :row-style="{height: '20px'}" @current-change="selectPlaybackRow" @row-dblclick="handleDblTableRow">
          <el-table-column label="任务ID" prop="id" align="center" width="98"> </el-table-column>
          <el-table-column label="回放任务名称" prop="dataTableName" align="center"></el-table-column>
          <!-- <el-table-column label="想定开始时间" prop="simStartTime" align="center"> </el-table-column> -->
          <el-table-column label="想定开始时间" prop="createTime" align="center"> </el-table-column>
          <el-table-column label="创建时间" prop="createTime" align="center"> </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-row class="flex-x-center">
                <el-popover placement="bottom" trigger="click" :popper-style="{background: '#00598c'}">
                  <template #reference>
                    <el-row class="align-items flex-x-center pointer mar_r_10">
                      <img src="/images/table_export_icon.png" alt="" class="mar_r_5" />
                      <span :style="{color: '#36b6ff'}">导出</span>
                    </el-row>
                  </template>
                  <template #default>
                    <el-row class="align-items flex-x-center pointer"
                      ><el-link class="mar_r_10 col_f" @click="exportPlayback(scope.row.dataTableName, 'dat')">dat</el-link>
                      <el-link class="mar_r_10 col_f" @click="exportPlayback(scope.row.dataTableName, 'txt')">txt</el-link>
                      <el-link class="col_f" @click="exportPlayback(scope.row.dataTableName, 'xml')">xml</el-link></el-row
                    >
                  </template>
                </el-popover>
                <el-row class="align-items flex-x-center pointer" @click.stop="deletePlayback(scope.row)">
                  <img src="/images/table_delete_icon.png" alt="" class="mar_r_5" />
                  <span :style="{color: '#36b6ff'}">删除</span>
                </el-row></el-row
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <el-row class="dis-flex flex-x-center mar_b_10 mar_t_10 dialog-footer">
      <el-button @click="dialogVisible = false">取&nbsp;消</el-button>
      <el-button @click="confim">确&nbsp;认</el-button>
    </el-row>
  </el-dialog>
</template>
<style lang="less" scoped>
.search-box {
  margin-bottom: 10px !important;
}

:deep(.el-table__cell) {
  border: 1px solid #1377b0;
  border-left: 0;
}

:deep(.el-table__cell:first-child) {
  border-left: solid 1px #0f6ca5;
}

:deep(.el-table__cell:last-child) {
  border-right: solid 1px #0f6ca5;
}

:deep(.el-table__row:last-child .el-table__cell) {
  border-bottom: 1px solid #1377b0;
}

.col_f {
  color: #fff;
}

.playback-list {
  border: solid 1px #1377b0;
  border-radius: 2px;

  .title {
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: #0c486a;
  }

  .content {
    padding: 4px 7px;
  }
}
</style>
