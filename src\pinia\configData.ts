/*
 * <AUTHOR> 老范
 * @Date         : 2022-08-19 17:27:50
 * @LastEditors: 老范
 * @LastEditTime: 2023-05-10 16:47:12
 * @Description  : 请填写简介
 */

import {defineStore} from 'pinia';

interface ConfigState {
  nodeDesign: any[]; //
  scenarioId: number; //
}

export const useConfigState = defineStore('configState', () => {
  const defaultState: ConfigState = {
    nodeDesign: [],
    scenarioId: 0,
  };
  return {...defaultState};
});
