<script setup lang="ts">
import {ref, onBeforeUnmount, PropType, reactive, computed} from 'vue';
import {useScenarioStore} from '@/pinia/scenario';

const nodeDesignRef = ref();
const scenarioStore = useScenarioStore();

// 定义tag数据结构
interface TagItem {
  id: string;
  name: string;
  type: string;
}

// 定义引擎节点实例结构
interface EngineNodeInstance {
  id: string;
  name: string; // 例如：*************-1
  ipAddress: string; // 原始IP地址
  instanceNumber: number; // 实例编号
  children: TreeNode[]; // 子节点（从资源树添加的内容）
  selected: boolean; // 是否被选中
  expanded: boolean; // 是否展开
}

// 定义树节点结构
interface TreeNode {
  id: string;
  name: string;
  type: string;
  level: number; // 层级：1-阵营，2-类别，3-实例
  children?: TreeNode[];
}

// 使用 scenario store 中的树数据，如果为空则使用默认数据
const treeData = computed(() => {
  if (scenarioStore.treeData && scenarioStore.treeData.length > 0) {
    return scenarioStore.treeData;
  }

  // 默认的树数据结构（当没有从 scenario 获取到数据时使用）
  return [
    {
      id: 'red_camp',
      name: '红方阵营',
      type: 'camp',
      level: 1,
      children: [
        {
          id: 'red_aircraft',
          name: '飞机',
          type: 'category',
          level: 2,
          children: [
            {id: 'red_fighter_1', name: '歼-20战斗机', type: 'aircraft', level: 3},
            {id: 'red_fighter_2', name: '歼-16战斗机', type: 'aircraft', level: 3},
            {id: 'red_bomber_1', name: '轰-6K轰炸机', type: 'aircraft', level: 3},
          ],
        },
        {
          id: 'red_ship',
          name: '舰船',
          type: 'category',
          level: 2,
          children: [
            {id: 'red_destroyer_1', name: '055型驱逐舰', type: 'ship', level: 3},
            {id: 'red_frigate_1', name: '054A型护卫舰', type: 'ship', level: 3},
          ],
        },
        {
          id: 'red_missile',
          name: '导弹',
          type: 'category',
          level: 2,
          children: [
            {id: 'red_missile_1', name: '鹰击-18反舰导弹', type: 'missile', level: 3},
            {id: 'red_missile_2', name: '红旗-9防空导弹', type: 'missile', level: 3},
          ],
        },
      ],
    },
    {
      id: 'blue_camp',
      name: '蓝方阵营',
      type: 'camp',
      level: 1,
      children: [
        {
          id: 'blue_aircraft',
          name: '飞机',
          type: 'category',
          level: 2,
          children: [
            {id: 'blue_fighter_1', name: 'F-22战斗机', type: 'aircraft', level: 3},
            {id: 'blue_fighter_2', name: 'F-35战斗机', type: 'aircraft', level: 3},
            {id: 'blue_bomber_1', name: 'B-2轰炸机', type: 'aircraft', level: 3},
          ],
        },
        {
          id: 'blue_ship',
          name: '舰船',
          type: 'category',
          level: 2,
          children: [
            {id: 'blue_destroyer_1', name: '阿利伯克级驱逐舰', type: 'ship', level: 3},
            {id: 'blue_cruiser_1', name: '提康德罗加级巡洋舰', type: 'ship', level: 3},
          ],
        },
        {
          id: 'blue_missile',
          name: '导弹',
          type: 'category',
          level: 2,
          children: [
            {id: 'blue_missile_1', name: '鱼叉反舰导弹', type: 'missile', level: 3},
            {id: 'blue_missile_2', name: '标准-6防空导弹', type: 'missile', level: 3},
          ],
        },
      ],
    },
  ] as TreeNode[];
});

// IP地址列表数据
const ipTags = reactive<TagItem[]>([
  {id: 'ip1', name: '*************', type: 'server'},
  {id: 'ip2', name: '*************', type: 'client'},
  {id: 'ip3', name: '*************', type: 'database'},
  {id: 'ip4', name: '*************', type: 'gateway'},
  {id: 'ip5', name: '*************', type: 'monitor'},
  {id: 'ip6', name: '*************', type: 'backup'},
]);

// 左侧面板切换状态：'compute' 或 'tree'
const leftPanelMode = ref<'compute' | 'tree'>('compute');

// 右侧引擎节点实例列表
const engineNodes = reactive<EngineNodeInstance[]>([]);

// IP地址实例计数器（用于生成递增编号）
const ipInstanceCounters = reactive<Record<string, number>>({});

// 选中的树节点和引擎节点
const selectedTreeNodes = reactive<Set<string>>(new Set());
const selectedEngineNodes = reactive<Set<string>>(new Set());

// 已添加到引擎节点的资源树节点ID集合
const addedTreeNodes = reactive<Set<string>>(new Set());

// 展开/折叠状态管理 - 默认展开阵营和类别级别
const expandedNodes = reactive<Set<string>>(
  new Set([
    'red_camp',
    'blue_camp', // 阵营级别
    'red_aircraft',
    'red_ship',
    'red_missile', // 红方类别
    'blue_aircraft',
    'blue_ship',
    'blue_missile', // 蓝方类别
  ])
);

// 添加新引擎节点实例的方法
const addInstance = (tagType: string, ipAddress: string) => {
  // 获取当前IP地址的实例计数
  if (!ipInstanceCounters[ipAddress]) {
    ipInstanceCounters[ipAddress] = 0;
  }
  ipInstanceCounters[ipAddress]++;

  // 创建新的引擎节点实例
  const newEngineNode: EngineNodeInstance = {
    id: `engine_${ipAddress.replace(/\./g, '_')}_${ipInstanceCounters[ipAddress]}`,
    name: `${ipAddress}-${ipInstanceCounters[ipAddress]}`,
    ipAddress: ipAddress,
    instanceNumber: ipInstanceCounters[ipAddress],
    children: [],
    selected: false,
    expanded: true,
  };

  engineNodes.push(newEngineNode);
  console.log('添加新引擎节点:', newEngineNode);
};

// 删除引擎节点实例的方法
const removeEngineNode = (engineNodeId: string) => {
  const index = engineNodes.findIndex(node => node.id === engineNodeId);
  if (index > -1) {
    const engineNode = engineNodes[index];

    // 恢复该引擎节点下所有资源的可选状态
    restoreResourceSelectability(engineNode);

    // 删除引擎节点
    engineNodes.splice(index, 1);
    // 同时从选中列表中移除
    selectedEngineNodes.delete(engineNodeId);
    console.log('删除引擎节点:', engineNodeId);
  }
};

// 恢复引擎节点下资源的可选状态
const restoreResourceSelectability = (engineNode: EngineNodeInstance) => {
  // 递归遍历引擎节点下的所有子项
  const restoreNodeChildren = (children: TreeNode[]) => {
    children.forEach(child => {
      if (child.level === 3) {
        // 装备级别：需要恢复原始资源树节点的可选状态
        restoreOriginalNodeSelectability(child);
      } else if (child.children) {
        // 阵营或类别级别：递归处理子项
        restoreNodeChildren(child.children);
      }
    });
  };

  if (engineNode.children && engineNode.children.length > 0) {
    restoreNodeChildren(engineNode.children);
  }
};

// 恢复原始资源树节点的可选状态
const restoreOriginalNodeSelectability = (equipmentNode: TreeNode) => {
  // 从引擎节点的装备名称和类型找到原始资源树节点
  const originalNode = findOriginalTreeNode(equipmentNode.name, equipmentNode.type);
  if (originalNode) {
    // 使用标记函数恢复可选状态
    markNodeAsSelectable(originalNode.id);
    console.log('恢复节点可选状态:', originalNode.name);
  }
};

// 根据名称和类型查找原始资源树节点
const findOriginalTreeNode = (name: string, type: string): TreeNode | null => {
  const searchInTree = (nodes: TreeNode[]): TreeNode | null => {
    for (const node of nodes) {
      // 检查当前节点
      if (node.name === name && node.type === type && node.level === 3) {
        return node;
      }
      // 递归检查子节点
      if (node.children) {
        const found = searchInTree(node.children);
        if (found) return found;
      }
    }
    return null;
  };

  return searchInTree(treeData.value);
};

// 删除引擎节点树中的节点
const deleteEngineTreeNode = (engineNodeId: string, nodeId: string) => {
  const engineNode = engineNodes.find(node => node.id === engineNodeId);
  if (!engineNode || !engineNode.children) {
    return;
  }

  // 递归删除节点及其子项
  const deleteNodeRecursively = (nodes: TreeNode[], targetId: string): boolean => {
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i];
      if (node.id === targetId) {
        // 找到目标节点，恢复其所有子项的可选状态
        restoreNodeAndChildrenSelectability(node);
        // 从数组中删除节点
        nodes.splice(i, 1);
        return true;
      }

      // 递归检查子节点
      if (node.children && deleteNodeRecursively(node.children, targetId)) {
        // 如果子节点被删除后，当前节点没有子项了，可以考虑是否删除当前节点
        if (node.children.length === 0) {
          // 如果是类别节点且没有装备了，可以删除类别节点
          if (node.level === 2) {
            nodes.splice(i, 1);
          }
        }
        return true;
      }
    }
    return false;
  };

  deleteNodeRecursively(engineNode.children, nodeId);

  // 清理空的阵营节点
  engineNode.children = engineNode.children.filter(camp => {
    if (camp.children && camp.children.length === 0) {
      return false; // 删除空的阵营节点
    }
    return true;
  });

  console.log('删除引擎树节点:', nodeId);
};

// 恢复节点及其所有子项的可选状态
const restoreNodeAndChildrenSelectability = (node: TreeNode) => {
  // 如果是装备节点，直接恢复可选状态
  if (node.level === 3) {
    restoreOriginalNodeSelectability(node);
  }

  // 递归处理子节点
  if (node.children) {
    node.children.forEach(child => {
      restoreNodeAndChildrenSelectability(child);
    });
  }
};

// 切换引擎节点选中状态（单选模式）
const toggleEngineNode = (engineNodeId: string) => {
  const engineNode = engineNodes.find(node => node.id === engineNodeId);
  if (engineNode) {
    // 如果当前节点已选中，取消选中
    if (engineNode.selected) {
      engineNode.selected = false;
      selectedEngineNodes.delete(engineNodeId);
    } else {
      // 取消所有其他节点的选中状态
      engineNodes.forEach(node => {
        if (node.id !== engineNodeId) {
          node.selected = false;
          selectedEngineNodes.delete(node.id);
        }
      });

      // 选中当前节点
      engineNode.selected = true;
      selectedEngineNodes.clear();
      selectedEngineNodes.add(engineNodeId);
    }
  }
};

// 切换引擎节点展开状态
const toggleEngineNodeExpanded = (engineNodeId: string) => {
  const engineNode = engineNodes.find(node => node.id === engineNodeId);
  if (engineNode) {
    engineNode.expanded = !engineNode.expanded;
  }
};

// 引擎节点内部树状结构的展开状态管理
const engineTreeExpandedNodes = reactive<Set<string>>(new Set());

// 切换引擎节点内部树节点的展开状态
const toggleEngineTreeNode = (nodeId: string) => {
  if (engineTreeExpandedNodes.has(nodeId)) {
    engineTreeExpandedNodes.delete(nodeId);
  } else {
    engineTreeExpandedNodes.add(nodeId);
  }
};

// 检查引擎节点内部树节点是否展开
const isEngineTreeNodeExpanded = (nodeId: string): boolean => {
  return engineTreeExpandedNodes.has(nodeId);
};

// 获取树节点的总子项数量（递归计算）
const getTotalChildrenCount = (node: TreeNode): number => {
  if (!node.children || node.children.length === 0) {
    return 0;
  }

  let count = 0;
  for (const child of node.children) {
    if (child.level === 3) {
      // 如果是装备级别，直接计数
      count++;
    } else {
      // 如果是阵营或类别级别，递归计算
      count += getTotalChildrenCount(child);
    }
  }
  return count;
};

// 展开所有引擎节点
const expandAllEngineNodes = () => {
  // 展开所有引擎节点本身
  engineNodes.forEach(node => {
    node.expanded = true;
  });

  // 展开所有引擎节点内部的树状结构
  const expandAllInternalNodes = (nodes: TreeNode[]) => {
    nodes.forEach(node => {
      engineTreeExpandedNodes.add(node.id);
      if (node.children) {
        expandAllInternalNodes(node.children);
      }
    });
  };

  engineNodes.forEach(engineNode => {
    if (engineNode.children) {
      expandAllInternalNodes(engineNode.children);
    }
  });
};

// 收缩所有引擎节点
const collapseAllEngineNodes = () => {
  // 收缩所有引擎节点本身
  engineNodes.forEach(node => {
    node.expanded = false;
  });

  // 收缩所有引擎节点内部的树状结构
  engineTreeExpandedNodes.clear();
};

// 切换展开/收缩所有引擎节点
const toggleExpandAllEngineNodes = () => {
  if (isAllEngineNodesExpanded.value) {
    collapseAllEngineNodes();
  } else {
    expandAllEngineNodes();
  }
};

// 检查是否所有引擎节点都已展开
const isAllEngineNodesExpanded = computed(() => {
  if (engineNodes.length === 0) {
    return false;
  }

  // 检查所有引擎节点本身是否展开
  const allEngineNodesExpanded = engineNodes.every(node => node.expanded);

  // 检查所有内部节点是否展开
  const getAllInternalNodeIds = (nodes: TreeNode[]): string[] => {
    const ids: string[] = [];
    nodes.forEach(node => {
      if (node.children && node.children.length > 0) {
        ids.push(node.id);
        ids.push(...getAllInternalNodeIds(node.children));
      }
    });
    return ids;
  };

  let allInternalNodesExpanded = true;
  engineNodes.forEach(engineNode => {
    if (engineNode.children) {
      const internalNodeIds = getAllInternalNodeIds(engineNode.children);
      if (!internalNodeIds.every(id => engineTreeExpandedNodes.has(id))) {
        allInternalNodesExpanded = false;
      }
    }
  });

  return allEngineNodesExpanded && allInternalNodesExpanded;
});

// 获取节点的所有子节点ID
const getAllChildrenIds = (node: TreeNode): string[] => {
  const ids: string[] = [];
  if (node.children) {
    for (const child of node.children) {
      ids.push(child.id);
      ids.push(...getAllChildrenIds(child));
    }
  }
  return ids;
};

// 获取节点的所有父节点ID
const getAllParentIds = (nodeId: string): string[] => {
  const parents: string[] = [];

  const findParents = (nodes: TreeNode[], targetId: string, currentParents: string[] = []): boolean => {
    for (const node of nodes) {
      if (node.id === targetId) {
        parents.push(...currentParents);
        return true;
      }
      if (node.children) {
        if (findParents(node.children, targetId, [...currentParents, node.id])) {
          return true;
        }
      }
    }
    return false;
  };

  findParents(treeData.value, nodeId);
  return parents;
};

// 检查节点的选中状态：'checked' | 'unchecked' | 'indeterminate'
const getNodeCheckState = (nodeId: string): 'checked' | 'unchecked' | 'indeterminate' => {
  const node = findTreeNode(nodeId);
  if (!node || !node.children) {
    return selectedTreeNodes.has(nodeId) ? 'checked' : 'unchecked';
  }

  const childrenIds = getAllChildrenIds(node);
  const checkedChildren = childrenIds.filter(id => selectedTreeNodes.has(id));

  if (checkedChildren.length === 0) {
    return 'unchecked';
  } else if (checkedChildren.length === childrenIds.length) {
    return 'checked';
  } else {
    return 'indeterminate';
  }
};

// 切换树节点选中状态（支持级联）
const toggleTreeNode = (nodeId: string) => {
  // 检查节点是否可选（未被添加的节点才能勾选）
  if (!isNodeSelectable(nodeId)) {
    return; // 已添加的节点不能被勾选
  }

  const node = findTreeNode(nodeId);
  if (!node) return;

  if (node.children && node.children.length > 0) {
    // 有子节点的情况
    const currentState = getNodeCheckState(nodeId);
    const shouldCheck = currentState !== 'checked';

    if (shouldCheck) {
      // 选中当前节点和所有子节点
      selectedTreeNodes.add(nodeId);
      const childrenIds = getAllChildrenIds(node);
      childrenIds.forEach(id => selectedTreeNodes.add(id));
    } else {
      // 取消选中当前节点和所有子节点
      selectedTreeNodes.delete(nodeId);
      const childrenIds = getAllChildrenIds(node);
      childrenIds.forEach(id => selectedTreeNodes.delete(id));
    }
  } else {
    // 叶子节点的情况
    if (selectedTreeNodes.has(nodeId)) {
      selectedTreeNodes.delete(nodeId);
    } else {
      selectedTreeNodes.add(nodeId);
    }
  }

  // 更新父节点状态
  updateParentStates(nodeId);
};

// 更新父节点的选中状态
const updateParentStates = (nodeId: string) => {
  const parentIds = getAllParentIds(nodeId);

  // 从最近的父节点开始更新，逐级向上
  parentIds.reverse().forEach(parentId => {
    const parentNode = findTreeNode(parentId);
    if (!parentNode || !parentNode.children) return;

    // 获取直接子节点的选中状态
    const directChildren = parentNode.children;
    const checkedDirectChildren = directChildren.filter(child => {
      if (child.children && child.children.length > 0) {
        // 如果子节点有子节点，检查其状态
        return getNodeCheckState(child.id) === 'checked';
      } else {
        // 如果是叶子节点，直接检查选中状态
        return selectedTreeNodes.has(child.id);
      }
    });

    if (checkedDirectChildren.length === directChildren.length) {
      // 所有直接子节点都选中，选中父节点
      selectedTreeNodes.add(parentId);
    } else {
      // 不是所有直接子节点都选中，取消选中父节点
      selectedTreeNodes.delete(parentId);
    }
  });
};

// 切换展开/折叠状态
const toggleExpanded = (nodeId: string) => {
  const node = findTreeNode(nodeId);
  if (!node) return;

  if (expandedNodes.has(nodeId)) {
    // 折叠节点：同时折叠所有子节点
    expandedNodes.delete(nodeId);
    collapseAllChildren(node);
  } else {
    // 展开节点
    expandedNodes.add(nodeId);
  }
};

// 递归折叠所有子节点
const collapseAllChildren = (node: TreeNode) => {
  if (node.children) {
    node.children.forEach(child => {
      expandedNodes.delete(child.id);
      collapseAllChildren(child);
    });
  }
};

// 展开所有节点
const expandAll = () => {
  const addAllNodes = (nodes: TreeNode[]) => {
    nodes.forEach(node => {
      if (node.children && node.children.length > 0) {
        expandedNodes.add(node.id);
        addAllNodes(node.children);
      }
    });
  };
  addAllNodes(treeData.value);
};

// 折叠所有节点
const collapseAll = () => {
  expandedNodes.clear();
};

// 检查是否所有节点都已展开
const isAllExpanded = () => {
  const getAllExpandableNodes = (nodes: TreeNode[]): string[] => {
    const expandableIds: string[] = [];
    nodes.forEach(node => {
      if (node.children && node.children.length > 0) {
        expandableIds.push(node.id);
        expandableIds.push(...getAllExpandableNodes(node.children));
      }
    });
    return expandableIds;
  };

  const allExpandableIds = getAllExpandableNodes(treeData.value);
  return allExpandableIds.every(id => expandedNodes.has(id));
};

// 切换全部展开/折叠状态
const toggleExpandAll = () => {
  if (isAllExpanded()) {
    collapseAll();
  } else {
    expandAll();
  }
};

// 切换左侧面板模式
const toggleLeftPanelMode = () => {
  leftPanelMode.value = leftPanelMode.value === 'tree' ? 'compute' : 'tree';
};

// 检查节点是否已被添加到引擎节点
const isNodeAdded = (nodeId: string): boolean => {
  return addedTreeNodes.has(nodeId);
};

// 检查节点是否可以被勾选（未被添加的节点才能勾选）
const isNodeSelectable = (nodeId: string): boolean => {
  // 如果节点本身已被添加，不能勾选
  if (isNodeAdded(nodeId)) {
    return false;
  }

  // 查找节点
  const node = findTreeNode(nodeId);
  if (!node) {
    return false;
  }

  // 如果是叶子节点，直接返回是否已添加的相反值
  if (!node.children || node.children.length === 0) {
    return !isNodeAdded(nodeId);
  }

  // 如果是父节点，检查是否有任何子节点可以勾选
  return hasSelectableChildren(node);
};

// 递归检查节点是否有可勾选的子节点
const hasSelectableChildren = (node: TreeNode): boolean => {
  if (!node.children || node.children.length === 0) {
    // 叶子节点：检查是否未被添加
    return !isNodeAdded(node.id);
  }

  // 父节点：检查是否有任何子节点可勾选
  return node.children.some(child => {
    if (!child.children || child.children.length === 0) {
      // 子节点是叶子节点
      return !isNodeAdded(child.id);
    } else {
      // 子节点是父节点，递归检查
      return hasSelectableChildren(child);
    }
  });
};

// 标记节点为已添加状态
const markNodeAsAdded = (nodeId: string) => {
  addedTreeNodes.add(nodeId);
  // 如果节点已被选中，取消选中状态
  if (selectedTreeNodes.has(nodeId)) {
    selectedTreeNodes.delete(nodeId);
  }

  // 检查并更新父节点的选中状态
  updateParentSelectionState(nodeId);
};

// 标记节点为可选状态（恢复可选性）
const markNodeAsSelectable = (nodeId: string) => {
  addedTreeNodes.delete(nodeId);

  // 检查并更新父节点的选中状态
  updateParentSelectionState(nodeId);
};

// 更新父节点的选中状态（当子节点状态改变时）
const updateParentSelectionState = (nodeId: string) => {
  const parentIds = getAllParentIds(nodeId);

  // 从最近的父节点开始检查
  parentIds.forEach(parentId => {
    // 如果父节点当前被选中，但已经不能勾选了，则取消选中
    if (selectedTreeNodes.has(parentId) && !isNodeSelectable(parentId)) {
      selectedTreeNodes.delete(parentId);
    }
  });
};

// 辅助函数：向引擎节点添加装备，确保阵营和类别只添加一次
const addEquipmentToEngineNode = (engineNode: EngineNodeInstance, campNode: TreeNode, categoryNode: TreeNode, equipmentNode: TreeNode) => {
  // 在引擎节点下查找或创建阵营节点
  let engineCampNode = engineNode.children.find(child => child.name === campNode.name && child.type === 'camp');

  if (!engineCampNode) {
    engineCampNode = {
      id: `${engineNode.id}_${campNode.id}`,
      name: campNode.name,
      type: 'camp',
      level: 1,
      children: [],
    };
    engineNode.children.push(engineCampNode);
  }

  // 在阵营节点下查找或创建类别节点
  let engineCategoryNode = engineCampNode.children!.find(child => child.name === categoryNode.name && child.type === 'category');

  if (!engineCategoryNode) {
    engineCategoryNode = {
      id: `${engineNode.id}_${categoryNode.id}`,
      name: categoryNode.name,
      type: 'category',
      level: 2,
      children: [],
    };
    engineCampNode.children!.push(engineCategoryNode);
  }

  // 检查装备是否已存在，避免重复添加
  const existingEquipment = engineCategoryNode.children!.find(child => child.name === equipmentNode.name && child.type === equipmentNode.type);

  if (!existingEquipment) {
    // 将具体装备添加到类别下
    const newEquipmentNode = {
      id: `${engineNode.id}_${equipmentNode.id}_${Date.now()}`,
      name: equipmentNode.name,
      type: equipmentNode.type,
      level: 3,
    };
    engineCategoryNode.children!.push(newEquipmentNode);

    // 标记原始节点为已添加状态
    markNodeAsAdded(equipmentNode.id);
  }
};

// 分配选中的树节点到所选引擎节点（添加到选中的引擎节点下，保持树状结构）
const moveToRight = () => {
  // 获取选中的引擎节点（单选模式，应该只有一个）
  const selectedEngineNodesList = engineNodes.filter(node => node.selected);

  if (selectedEngineNodesList.length === 0) {
    console.warn('请先选择一个引擎节点');
    return;
  }

  if (selectedEngineNodesList.length > 1) {
    console.warn('只能选择一个引擎节点');
    return;
  }

  // 将选中的树节点添加到每个选中的引擎节点下，保持完整的树状结构
  selectedTreeNodes.forEach(nodeId => {
    const treeNode = findTreeNode(nodeId);
    if (treeNode) {
      // 添加到每个选中的引擎节点
      selectedEngineNodesList.forEach(engineNode => {
        // 根据选中节点的层级进行不同处理
        if (treeNode.level === 3) {
          // 叶子节点（具体装备）：重建完整层级结构
          const parentIds = getAllParentIds(nodeId);
          if (parentIds.length >= 2) {
            const categoryId = parentIds[parentIds.length - 1]; // 直接父级（类别）
            const campId = parentIds[parentIds.length - 2]; // 阵营

            const categoryNode = findTreeNode(categoryId);
            const campNode = findTreeNode(campId);

            if (categoryNode && campNode) {
              addEquipmentToEngineNode(engineNode, campNode, categoryNode, treeNode);
            }
          }
        } else if (treeNode.level === 2) {
          // 类别节点：添加整个类别及其装备
          const parentIds = getAllParentIds(nodeId);
          if (parentIds.length >= 1) {
            const campId = parentIds[parentIds.length - 1]; // 阵营
            const campNode = findTreeNode(campId);

            if (campNode && treeNode.children) {
              // 为该类别下的每个装备调用添加方法
              treeNode.children.forEach(equipment => {
                addEquipmentToEngineNode(engineNode, campNode, treeNode, equipment);
              });
            }
          }
        } else if (treeNode.level === 1) {
          // 阵营节点：添加整个阵营及其所有内容
          if (treeNode.children) {
            treeNode.children.forEach(category => {
              if (category.children) {
                category.children.forEach(equipment => {
                  addEquipmentToEngineNode(engineNode, treeNode, category, equipment);
                });
              }
            });
          }
        }
      });
    }
  });

  selectedTreeNodes.clear();
  console.log('已将选中的树节点添加到引擎节点，保持树状结构');
};

// 创建树节点的深拷贝
const createTreeNodeCopy = (node: TreeNode, idPrefix: string): TreeNode => {
  const copy: TreeNode = {
    id: `${idPrefix}_${node.id}`,
    name: node.name,
    type: node.type,
    level: node.level,
  };

  if (node.children && node.children.length > 0) {
    copy.children = node.children.map(child => createTreeNodeCopy(child, idPrefix));
  }

  return copy;
};

// 查找树节点的辅助函数（支持三级结构）
const findTreeNode = (nodeId: string): TreeNode | null => {
  const searchInNode = (node: TreeNode): TreeNode | null => {
    if (node.id === nodeId) return node;
    if (node.children) {
      for (const child of node.children) {
        const found = searchInNode(child);
        if (found) return found;
      }
    }
    return null;
  };

  for (const root of treeData.value) {
    const found = searchInNode(root);
    if (found) return found;
  }
  return null;
};

defineExpose({
  addInstance,
  removeEngineNode,
  deleteEngineTreeNode,
  restoreResourceSelectability,
  restoreOriginalNodeSelectability,
  restoreNodeAndChildrenSelectability,
  findOriginalTreeNode,
  markNodeAsSelectable,
  toggleEngineNode,
  toggleEngineNodeExpanded,
  toggleEngineTreeNode,
  isEngineTreeNodeExpanded,
  getTotalChildrenCount,
  expandAllEngineNodes,
  collapseAllEngineNodes,
  toggleExpandAllEngineNodes,
  isAllEngineNodesExpanded,
  isNodeSelectable,
  hasSelectableChildren,
  moveToRight,
  expandAll,
  collapseAll,
  toggleExpanded,
  toggleExpandAll,
  isAllExpanded,
  toggleLeftPanelMode,
});
</script>

<template>
  <div ref="nodeDesignRef" class="node-design-container">
    <!-- 设计工作区 -->
    <div class="workspace-section">
      <div class="workspace-content">
        <!-- 左侧：可切换的节点计算框和资源树 -->
        <div class="workspace-left">
          <div class="left-panel-header">
            <h4>{{ leftPanelMode === 'tree' ? '资源树' : '节点计算框' }}</h4>
            <div class="left-panel-controls">
              <!-- 展开/折叠按钮 - 仅在资源树模式下显示 -->
              <button v-if="leftPanelMode === 'tree'" class="panel-mode-btn" :title="isAllExpanded() ? '折叠所有' : '展开所有'" @click="toggleExpandAll">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline v-if="isAllExpanded()" points="9,18 15,12 9,6"></polyline>
                  <polyline v-else points="6,9 12,15 18,9"></polyline>
                </svg>
              </button>
              <!-- 模式切换按钮 -->
              <button class="panel-mode-btn" :title="leftPanelMode === 'tree' ? '切换到节点计算框' : '切换到资源树'" @click="toggleLeftPanelMode">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path v-if="leftPanelMode === 'tree'" d="M3 3h18v18H3zM9 9h6v6H9z"></path>
                  <path v-else d="M3 3h18v18H3zM7 7h10v10H7z"></path>
                </svg>
              </button>
            </div>
          </div>

          <!-- 节点计算框内容 -->
          <div v-if="leftPanelMode === 'compute'" class="compute-box-container">
            <div class="ip-tags-list">
              <div v-for="tag in ipTags" :key="tag.id" class="ip-tag-item-vertical" :class="`tag-${tag.type}`">
                <div class="ip-tag-content">
                  <span class="ip-address">{{ tag.name }}</span>
                  <span class="ip-type">{{ tag.type }}</span>
                </div>
                <button class="add-btn" title="添加引擎节点" @click="addInstance(tag.type, tag.name)">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- 资源树内容 -->
          <div v-if="leftPanelMode === 'tree'" class="tree-container">
            <!-- 第一级：阵营 -->
            <div v-for="camp in treeData" :key="camp.id" class="tree-node level-1">
              <div class="tree-node-item camp" :class="{selected: selectedTreeNodes.has(camp.id), disabled: !isNodeSelectable(camp.id)}">
                <div class="tree-node-content">
                  <div class="checkbox-container" @click="toggleTreeNode(camp.id)">
                    <input type="checkbox" :checked="getNodeCheckState(camp.id) === 'checked'" :indeterminate="getNodeCheckState(camp.id) === 'indeterminate'" :disabled="!isNodeSelectable(camp.id)" class="tree-checkbox" @click.stop="toggleTreeNode(camp.id)" />
                    <span v-if="getNodeCheckState(camp.id) === 'indeterminate'" class="indeterminate-mark">-</span>
                  </div>
                  <span class="tree-expand-icon" @click.stop="toggleExpanded(camp.id)">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <polyline v-if="expandedNodes.has(camp.id)" points="6,9 12,15 18,9"></polyline>
                      <polyline v-else points="9,18 15,12 9,6"></polyline>
                    </svg>
                  </span>
                  <span class="tree-label" :class="camp.id.includes('red') ? 'red-camp' : 'blue-camp'" :title="camp.name" @click="toggleTreeNode(camp.id)">
                    {{ camp.name }}
                  </span>
                </div>
              </div>

              <!-- 第二级：类别 -->
              <div v-if="camp.children && expandedNodes.has(camp.id)" class="tree-children level-2">
                <div v-for="category in camp.children" :key="category.id" class="tree-node">
                  <div class="tree-node-item category" :class="{selected: selectedTreeNodes.has(category.id), disabled: !isNodeSelectable(category.id)}">
                    <div class="tree-node-content">
                      <div class="checkbox-container" @click="toggleTreeNode(category.id)">
                        <input type="checkbox" :checked="getNodeCheckState(category.id) === 'checked'" :indeterminate="getNodeCheckState(category.id) === 'indeterminate'" :disabled="!isNodeSelectable(category.id)" class="tree-checkbox" @click.stop="toggleTreeNode(category.id)" />
                        <span v-if="getNodeCheckState(category.id) === 'indeterminate'" class="indeterminate-mark">-</span>
                      </div>
                      <span class="tree-expand-icon" @click.stop="toggleExpanded(category.id)">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <polyline v-if="expandedNodes.has(category.id)" points="6,9 12,15 18,9"></polyline>
                          <polyline v-else points="9,18 15,12 9,6"></polyline>
                        </svg>
                      </span>
                      <span class="tree-label" :title="category.name" @click="toggleTreeNode(category.id)">{{ category.name }}</span>
                    </div>
                  </div>

                  <!-- 第三级：具体实例 -->
                  <div v-if="category.children && expandedNodes.has(category.id)" class="tree-children level-3">
                    <div v-for="instance in category.children" :key="instance.id" class="tree-node">
                      <div
                        class="tree-node-item instance"
                        :class="{
                          selected: selectedTreeNodes.has(instance.id),
                          disabled: !isNodeSelectable(instance.id),
                          [`type-${instance.type}`]: true,
                        }"
                      >
                        <div class="checkbox-container" @click="toggleTreeNode(instance.id)">
                          <input type="checkbox" :checked="selectedTreeNodes.has(instance.id)" :disabled="!isNodeSelectable(instance.id)" class="tree-checkbox" @click.stop="toggleTreeNode(instance.id)" />
                        </div>
                        <span class="tree-label" :title="instance.name" @click="toggleTreeNode(instance.id)">{{ instance.name }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 中间：分配按钮 -->
        <div class="workspace-center">
          <div class="move-buttons">
            <button class="move-btn assign-btn" :disabled="selectedTreeNodes.size === 0 || selectedEngineNodes.size === 0" title="分配至所选节点" @click="moveToRight">
              <span class="btn-text">节点分配</span>
            </button>
          </div>
        </div>

        <!-- 右侧：引擎节点 -->
        <div class="workspace-right">
          <div class="instances-header">
            <div class="header-title">
              <h4>引擎节点</h4>
              <span class="instance-count">{{ engineNodes.length }}</span>
            </div>
            <div class="right-panel-controls">
              <button class="panel-mode-btn" :title="isAllEngineNodesExpanded ? '折叠' : '展开'" @click="toggleExpandAllEngineNodes">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline v-if="isAllEngineNodesExpanded" points="6,9 12,15 18,9"></polyline>
                  <polyline v-else points="9,18 15,12 9,6"></polyline>
                </svg>
              </button>
            </div>
          </div>
          <div class="instances-container">
            <div v-if="engineNodes.length === 0" class="empty-placeholder">
              <p>点击左侧的添加按钮来创建引擎节点</p>
            </div>

            <!-- 引擎节点列表 -->
            <div v-for="engineNode in engineNodes" :key="engineNode.id" class="engine-node-item">
              <!-- 引擎节点头部 -->
              <div class="engine-node-header" :class="{selected: engineNode.selected}" @click="toggleEngineNode(engineNode.id)">
                <div class="engine-node-info">
                  <div class="engine-node-checkbox">
                    <input type="checkbox" :checked="engineNode.selected" @click.stop @change="toggleEngineNode(engineNode.id)" />
                  </div>
                  <div class="engine-node-expand" @click.stop="toggleEngineNodeExpanded(engineNode.id)">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <polyline v-if="engineNode.expanded" points="6,9 12,15 18,9"></polyline>
                      <polyline v-else points="9,18 15,12 9,6"></polyline>
                    </svg>
                  </div>
                  <div class="engine-node-name" :title="engineNode.name">{{ engineNode.name }}</div>
                  <div class="engine-node-count">({{ engineNode.children.length }})</div>
                </div>
                <div class="engine-node-actions">
                  <button class="engine-node-delete-btn" title="删除引擎节点" @click.stop="removeEngineNode(engineNode.id)">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </button>
                </div>
              </div>

              <!-- 引擎节点子项（树状结构） -->
              <div v-if="engineNode.expanded && engineNode.children.length > 0" class="engine-node-children">
                <div v-for="child in engineNode.children" :key="child.id" class="engine-tree-node" :class="`level-${child.level}`">
                  <!-- 阵营级别 -->
                  <div v-if="child.level === 1" class="engine-camp-node">
                    <div class="engine-camp-header">
                      <div class="engine-camp-expand" @click="toggleEngineTreeNode(child.id)">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <polyline v-if="isEngineTreeNodeExpanded(child.id)" points="6,9 12,15 18,9"></polyline>
                          <polyline v-else points="9,18 15,12 9,6"></polyline>
                        </svg>
                      </div>
                      <span class="engine-camp-name" :class="child.name.includes('红') ? 'red-camp' : 'blue-camp'" :title="child.name">
                        {{ child.name }}
                      </span>
                      <span class="engine-node-count">({{ getTotalChildrenCount(child) }})</span>
                      <button class="engine-tree-delete-btn" title="删除阵营" @click.stop="deleteEngineTreeNode(engineNode.id, child.id)">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <polyline points="3,6 5,6 21,6"></polyline>
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                        </svg>
                      </button>
                    </div>

                    <!-- 阵营下的类别 -->
                    <div v-if="isEngineTreeNodeExpanded(child.id) && child.children" class="engine-camp-children">
                      <div v-for="category in child.children" :key="category.id" class="engine-category-node">
                        <div class="engine-category-header">
                          <div class="engine-category-expand" @click="toggleEngineTreeNode(category.id)">
                            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                              <polyline v-if="isEngineTreeNodeExpanded(category.id)" points="6,9 12,15 18,9"></polyline>
                              <polyline v-else points="9,18 15,12 9,6"></polyline>
                            </svg>
                          </div>
                          <span class="engine-category-name" :title="category.name">{{ category.name }}</span>
                          <span class="engine-node-count">({{ category.children ? category.children.length : 0 }})</span>
                          <button class="engine-tree-delete-btn" title="删除类别" @click.stop="deleteEngineTreeNode(engineNode.id, category.id)">
                            <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                              <polyline points="3,6 5,6 21,6"></polyline>
                              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                            </svg>
                          </button>
                        </div>

                        <!-- 类别下的具体装备 -->
                        <div v-if="isEngineTreeNodeExpanded(category.id) && category.children" class="engine-category-children">
                          <div v-for="equipment in category.children" :key="equipment.id" class="engine-equipment-node">
                            <span class="equipment-name" :title="equipment.name">{{ equipment.name }}</span>
                            <!-- <span class="equipment-type">{{ equipment.type }}</span> -->
                            <button class="engine-tree-delete-btn equipment-delete" title="删除装备" @click.stop="deleteEngineTreeNode(engineNode.id, equipment.id)">
                              <svg width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="3,6 5,6 21,6"></polyline>
                                <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 类别级别（直接选中类别的情况） -->
                  <div v-else-if="child.level === 2" class="engine-category-node">
                    <div class="engine-category-header">
                      <div class="engine-category-expand" @click="toggleEngineTreeNode(child.id)">
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <polyline v-if="isEngineTreeNodeExpanded(child.id)" points="6,9 12,15 18,9"></polyline>
                          <polyline v-else points="9,18 15,12 9,6"></polyline>
                        </svg>
                      </div>
                      <span class="engine-category-name" :title="child.name">{{ child.name }}</span>
                      <span class="engine-node-count">({{ child.children ? child.children.length : 0 }})</span>
                      <button class="engine-tree-delete-btn" title="删除类别" @click.stop="deleteEngineTreeNode(engineNode.id, child.id)">
                        <svg width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                          <polyline points="3,6 5,6 21,6"></polyline>
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                        </svg>
                      </button>
                    </div>

                    <div v-if="isEngineTreeNodeExpanded(child.id) && child.children" class="engine-category-children">
                      <div v-for="equipment in child.children" :key="equipment.id" class="engine-equipment-node">
                        <span class="equipment-name" :title="equipment.name">{{ equipment.name }}</span>
                        <span class="equipment-type">{{ equipment.type }}</span>
                        <button class="engine-tree-delete-btn equipment-delete" title="删除装备" @click.stop="deleteEngineTreeNode(engineNode.id, equipment.id)">
                          <svg width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="3,6 5,6 21,6"></polyline>
                            <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- 装备级别（直接选中装备的情况） -->
                  <div v-else-if="child.level === 3" class="engine-equipment-node">
                    <span class="equipment-name" :title="child.name">{{ child.name }}</span>
                    <span class="equipment-type">{{ child.type }}</span>
                    <button class="engine-tree-delete-btn equipment-delete" title="删除装备" @click.stop="deleteEngineTreeNode(engineNode.id, child.id)">
                      <svg width="8" height="8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="3,6 5,6 21,6"></polyline>
                        <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.node-design-container {
  display: flex;
  flex-direction: column;
  background-color: var(--panel-bg-color, #002f49);
  color: var(--font-color-white, #fff);
  font-family: Regular, sans-serif;
}

.section-header {
  padding: 12px 16px;
  background-color: var(--title-bg-color, #12577f);
  border-bottom: 1px solid var(--border-color, #32aff7);

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--title-color, #fff);
    font-family: Medium, sans-serif;
  }
}

.add-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #4caf50;
  color: white;

  &:hover {
    transform: scale(1.1);
    background-color: #45a049;
  }
}

/* 设计工作区域 */
.workspace-section {
  flex: 1;
  // border: var(--border, 1px solid #32aff7);
  // border-radius: 8px;
  margin: 16px;
  // background-color: var(--dialog-bg-color, #022e48);
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  max-height: 80vh;
  height: 60vh;
}

.workspace-content {
  // flex: 1;
  padding: 16px;
  display: flex;
  gap: 16px;
  height: 80vh;
}

/* 左侧：可切换的面板 */
.workspace-left {
  width: 300px; /* 固定宽度 */
  min-width: 300px; /* 最小宽度 */
  max-width: 300px; /* 最大宽度 */
  border: var(--border, 1px solid #32aff7);
  border-radius: 8px;
  background-color: var(--panel-bg-color, #002f49);
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出 */
}

.left-panel-header {
  padding: 12px 16px;
  background-color: var(--title-bg-color, #12577f);
  border-bottom: 1px solid var(--split-line-color, #3186b3);
  border-radius: 8px 8px 0 0;
  display: flex;
  align-items: center;
  justify-content: space-between;

  h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--title-color, #fff);
    font-family: Medium, sans-serif;
  }
}

.left-panel-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-mode-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: 1px solid var(--border-color, #32aff7);
  border-radius: 4px;
  background-color: var(--panel-bg-color, #002f49);
  color: var(--icon-color, #5fc5ff);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--select-hover-color, #0a79b8);
    border-color: var(--selected-font-color, #36b6ff);
    color: var(--font-color-white, #fff);
  }

  &:active {
    background-color: var(--highlight-color, #006ca9);
  }
}

/* 节点计算框容器 */
.compute-box-container {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
  // max-height: calc(80vh - 200px); /* 减去头部和其他元素的高度 */
}

.ip-tags-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ip-tag-item-vertical {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid var(--split-line-color, #3186b3);
  background-color: var(--dialog-bg-color, #022e48);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transform: translateY(-1px);
    border-color: var(--selected-font-color, #36b6ff);
  }

  &.tag-server {
    border-left: 4px solid var(--icon-color, #5fc5ff);
  }

  &.tag-client {
    border-left: 4px solid var(--selected-font-color, #36b6ff);
  }

  &.tag-database {
    border-left: 4px solid #ff9800;
  }

  &.tag-gateway {
    border-left: 4px solid #9c27b0;
  }

  &.tag-monitor {
    border-left: 4px solid #f44336;
  }

  &.tag-backup {
    border-left: 4px solid #607d8b;
  }
}

.ip-tag-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.ip-address {
  font-size: 14px;
  font-weight: 600;
  color: var(--font-color-white, #fff);
  font-family: 'Courier New', monospace;
}

.ip-type {
  font-size: 12px;
  color: var(--font-color, #c3f8ff);
  text-transform: uppercase;
  font-weight: 500;
}

.tree-container {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
  // max-height: calc(80vh - 200px); /* 减去头部和其他元素的高度 */
}

.tree-node {
  margin-bottom: 4px;
}

.tree-node-item {
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--hover-bg, rgba(0, 162, 255, 0.4));
  }

  &.selected {
    background-color: var(--selected-font-color, #36b6ff);
    color: var(--font-color-white, #fff);
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;

    .tree-label {
      color: var(--translucent-grey, rgba(255, 255, 255, 0.5));
      cursor: not-allowed;
    }

    .checkbox-container {
      cursor: not-allowed;
    }

    .tree-node-content {
      background-color: var(--bg-dark, #040b22);
    }

    &:hover {
      background-color: transparent;
    }
  }

  // 第一级：阵营
  &.camp {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 8px;

    .tree-node-content {
      display: flex;
      align-items: center;
      padding: 12px;
      border-radius: 8px;
      background-color: var(--title-bg-color, #12577f);
      border: 2px solid var(--split-line-color, #3186b3);
    }

    &.selected .tree-node-content {
      background-color: var(--selected-font-color, #36b6ff);
      color: var(--font-color-white, #fff);
      border-color: var(--border-color, #32aff7);
    }
  }

  // 第二级：类别
  &.category {
    font-weight: 500;
    font-size: 14px;
    margin-left: 20px;
    margin-bottom: 4px;

    .tree-node-content {
      display: flex;
      align-items: center;
      padding: 10px;
      border-radius: 6px;
      background-color: var(--panel-bg-color, #002f49);
      border-left: 3px solid var(--icon-color, #5fc5ff);
    }

    &.selected .tree-node-content {
      background-color: var(--select-hover-color, #0a79b8);
      color: var(--font-color-white, #fff);
    }
  }

  // 第三级：实例
  &.instance {
    font-size: 13px;
    margin-left: 40px;
    margin-bottom: 2px;

    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    background-color: var(--dialog-bg-color, #022e48);
    border: 1px solid var(--split-line-color, #3186b3);

    &.selected {
      background-color: var(--selected-font-color, #36b6ff);
      color: var(--font-color-white, #fff);
      border-color: var(--border-color, #32aff7);
    }

    &.type-aircraft {
      border-left: 3px solid #4caf50;
    }

    &.type-ship {
      border-left: 3px solid var(--icon-color, #5fc5ff);
    }

    &.type-missile {
      border-left: 3px solid #ff9800;
    }
  }
}

.tree-icon {
  margin-right: 8px;
  font-size: 16px;
}

.tree-label {
  flex: 1;
  cursor: pointer;
  user-select: none;
  padding: 2px 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  color: var(--font-color-white, #fff);

  /* 文本溢出处理 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0; /* 允许flex子项收缩 */

  &:hover {
    background-color: var(--hover-bg, rgba(0, 162, 255, 0.4));
  }

  &.red-camp {
    color: #ff6b6b;
    font-weight: 700;
  }

  &.blue-camp {
    color: var(--selected-font-color, #36b6ff);
    font-weight: 700;
  }
}

.checkbox-container {
  position: relative;
  display: inline-block;
  margin-right: 8px;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(33, 150, 243, 0.1);
  }
}

.checkbox-container {
  position: relative;
  display: inline-block;
  margin-right: 8px;
}

.tree-checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;

  &:indeterminate {
    background-color: #2196f3;
    border-color: #2196f3;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
    background-color: #f5f5f5;
  }
}

.indeterminate-mark {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
  pointer-events: none;
  line-height: 1;
}

.tree-expand-icon {
  margin-right: 8px;
  cursor: pointer;
  user-select: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(33, 150, 243, 0.1);
    transform: scale(1.1);
  }

  svg {
    transition: transform 0.2s ease;
  }
}

.tree-node-content {
  width: 100%;
  display: flex;
  align-items: center;
  min-width: 0; /* 允许flex子项收缩 */
  overflow: hidden; /* 防止内容溢出 */
}

.tree-children {
  margin-top: 4px;

  &.level-2 {
    margin-left: 0;
  }

  &.level-3 {
    margin-left: 0;
  }
}

/* 中间：移动按钮 */
.workspace-center {
  flex: 0 0 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.move-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.move-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: 2px solid var(--border-color, #32aff7);
  border-radius: 8px;
  background-color: var(--panel-bg-color, #002f49);
  color: var(--font-color, #c3f8ff);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  font-family: Regular, sans-serif;

  &:hover:not(:disabled) {
    background-color: var(--selected-font-color, #36b6ff);
    color: var(--font-color-white, #fff);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(54, 182, 255, 0.3);
  }

  &:disabled {
    border-color: var(--translucent-grey, rgba(255, 255, 255, 0.5));
    color: var(--translucent-grey, rgba(255, 255, 255, 0.5));
    cursor: not-allowed;
    opacity: 0.5;
  }

  &.move-right {
    flex-direction: row;
  }

  &.assign-btn {
    min-width: 90px;
    justify-content: center;

    .btn-text {
      font-size: 14px;
      font-weight: 500;
      white-space: nowrap;
    }
  }
}

/* 右侧：节点实例 */
.workspace-right {
  width: 350px; /* 固定宽度 */
  min-width: 350px; /* 最小宽度 */
  max-width: 350px; /* 最大宽度 */
  border: var(--border, 1px solid #32aff7);
  border-radius: 8px;
  background-color: var(--panel-bg-color, #002f49);
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出 */
}

.instances-header {
  padding: 12px 16px;
  background-color: var(--title-bg-color, #12577f);
  border-bottom: 1px solid var(--split-line-color, #3186b3);
  border-radius: 8px 8px 0 0;
  display: flex;
  align-items: center;
  justify-content: space-between;

  h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--title-color, #fff);
    font-family: Medium, sans-serif;
  }
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.right-panel-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.instance-count {
  background-color: var(--selected-font-color, #36b6ff);
  color: var(--font-color-white, #fff);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.instances-container {
  flex: 1;
  padding: 8px;
  overflow-y: auto;
  max-height: calc(80vh - 200px); /* 减去头部和其他元素的高度 */
  background-color: var(--panel-bg-color, #002f49);
}

.instance-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);

    .instance-actions {
      opacity: 1;
    }
  }

  &.selected {
    border-color: #2196f3;
    background-color: #e3f2fd;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
  }

  &.type-input {
    border-left: 4px solid #4caf50;
  }

  &.type-process {
    border-left: 4px solid #2196f3;
  }

  &.type-output {
    border-left: 4px solid #ff9800;
  }

  &.type-filter {
    border-left: 4px solid #9c27b0;
  }
}

.instance-icon {
  margin-right: 12px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: #f5f5f5;
  border-radius: 6px;
}

.instance-info {
  flex: 1;
}

.instance-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.instance-type {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  font-weight: 500;
}

.instance-actions {
  opacity: 0;
  transition: opacity 0.2s ease;
  margin-left: 8px;
}

.instance-delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 50%;
  background-color: #f44336;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #da190b;
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }
}

.empty-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--font-color, #c3f8ff);
  font-size: 14px;
  padding: 20px;

  p {
    margin: 0;
    line-height: 1.5;
  }
}

/* 引擎节点样式 */
.engine-node-item {
  margin-bottom: 8px;
  border: 1px solid var(--split-line-color, #3186b3);
  border-radius: 6px;
  background-color: var(--dialog-bg-color, #022e48);
  overflow: hidden;
}

.engine-node-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--title-bg-color, #12577f);
  border-bottom: 1px solid var(--split-line-color, #3186b3);

  &:hover {
    background-color: var(--select-hover-color, #0a79b8);
  }

  &.selected {
    background-color: var(--selected-font-color, #36b6ff);
    border-color: var(--border-color, #32aff7);
  }
}

.engine-node-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.engine-node-checkbox {
  display: flex;
  align-items: center;
}

.engine-node-expand {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: var(--icon-color, #5fc5ff);

  &:hover {
    background-color: var(--hover-bg, rgba(0, 162, 255, 0.4));
  }
}

.engine-node-name {
  font-weight: 600;
  color: var(--font-color-white, #fff);
  font-family: 'Courier New', monospace;

  /* 文本溢出处理 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0; /* 允许flex子项收缩 */
  flex: 1; /* 占用剩余空间 */
}

.engine-node-count {
  font-size: 12px;
  color: var(--font-color-white, #fff);
  background-color: var(--selected-font-color, #36b6ff);
  padding: 2px 6px;
  border-radius: 10px;
}

.engine-node-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.engine-node-delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  background-color: transparent;
  color: var(--font-color, #c3f8ff);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: rgba(244, 67, 54, 0.2);
    color: #ff6b6b;
  }
}

.engine-node-children {
  padding: 8px 12px;
  background-color: var(--panel-bg-color, #002f49);
}

.engine-child-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 4px;
  background-color: white;
  border: 1px solid #e0e0e0;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.child-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.child-name {
  font-weight: 500;
  color: #333;
}

.child-type {
  font-size: 12px;
  color: #666;
  background-color: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
}

/* 引擎节点内部树状结构样式 */
.engine-tree-node {
  margin-bottom: 4px;
}

.engine-camp-node {
  margin-bottom: 8px;
}

.engine-camp-header {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px;
  background-color: var(--title-bg-color, #12577f);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--select-hover-color, #0a79b8);
  }
}

.engine-camp-expand {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  cursor: pointer;
  border-radius: 3px;
  transition: all 0.2s ease;
  color: var(--icon-color, #5fc5ff);

  &:hover {
    background-color: var(--hover-bg, rgba(0, 162, 255, 0.4));
  }
}

.engine-camp-name {
  font-weight: 600;
  font-size: 14px;

  /* 文本溢出处理 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
  flex: 1;

  &.red-camp {
    color: #ff6b6b;
  }

  &.blue-camp {
    color: var(--selected-font-color, #36b6ff);
  }
}

.engine-camp-children {
  margin-top: 6px;
  margin-left: 20px;
}

.engine-category-node {
  margin-bottom: 6px;
}

.engine-category-header {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  background-color: var(--dialog-bg-color, #022e48);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--split-line-color, #3186b3);

  &:hover {
    background-color: var(--select-hover-color, #0a79b8);
  }
}

.engine-category-expand {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  cursor: pointer;
  border-radius: 3px;
  transition: all 0.2s ease;
  color: var(--icon-color, #5fc5ff);

  &:hover {
    background-color: var(--hover-bg, rgba(0, 162, 255, 0.4));
  }
}

.engine-category-name {
  font-weight: 500;
  font-size: 13px;
  color: var(--font-color-white, #fff);

  /* 文本溢出处理 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
  flex: 1;
}

.engine-category-children {
  margin-top: 4px;
  margin-left: 18px;
}

.engine-equipment-node {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  margin-bottom: 2px;
  background-color: var(--bg-dark, #040b22);
  border-radius: 3px;
  border-left: 3px solid var(--icon-color, #5fc5ff);
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--select-hover-color, #0a79b8);
    transform: translateX(2px);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.equipment-name {
  font-weight: 500;
  font-size: 12px;
  color: var(--font-color-white, #fff);
  flex: 1;

  /* 文本溢出处理 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  min-width: 0;
}

.equipment-type {
  font-size: 10px;
  color: var(--font-color, #c3f8ff);
  background-color: var(--split-line-color, #3186b3);
  padding: 1px 4px;
  border-radius: 2px;
  text-transform: uppercase;
}

/* 引擎树删除按钮样式 */
.engine-tree-delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 3px;
  background-color: transparent;
  color: var(--font-color, #c3f8ff);
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.7;
  margin-left: auto;

  &:hover {
    background-color: rgba(244, 67, 54, 0.2);
    color: #ff6b6b;
    opacity: 1;
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }

  &.equipment-delete {
    width: 16px;
    height: 16px;

    svg {
      width: 8px;
      height: 8px;
    }
  }

  svg {
    transition: all 0.2s ease;
  }
}
</style>
