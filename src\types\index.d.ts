/*
 * <AUTHOR> 老范
 * @Date         : 2022-08-12 13:38:01
 * @LastEditors: liukun
 * @LastEditTime: 2024-09-29 16:43:17
 * @Description  : 公用types
 */

export interface MyResponseType<T = any> {
  code?: number;
  message?: string;
  data: T;
  status: number;
}
// 想定信息
export interface ScenarioType {
  id: number;
  name: string;
  createTime: string;
  endTime: string;
  updateTime: string;
  startTime: string;
  username: string;
  description: string;
}
// 想定存档信息
export interface FileKeepType {
  id: number;
  total: number;
  scenarioId: number;
  createTime: string;
  updateTime: string;
  startTime: string;
  archives: ArchiveType[];
}
export interface ArchiveType {
  id: number;
  archiveId: number;
  archiveMode: boolean;
  createTime: string;
  simTime: string;
  deductionMode: boolean;
  description: string;
  speed: number;
  timeStamp: number;
  updateTime: string;
  step: number;
}
// 回放信息
export interface PlayBackType {
  createTime: string;
  dataTableName: string;
  id: number;
  realSimDuration: string;
  scenarioId: number;
  simDuration: string;
  simStartTime: string;
  type: number;
  updateTime: string;
}
// 回放事件信息
export interface PlayBackEventType {
  timeStamp: number;
  event: {
    type: string;
    infos: {}[];
  }[];
}

// 想定模型参数
export interface ScenarioModelParam {
  name: string;
  value: any;
  type?: string;
  description?: string;
}

// 想定模型信息
export interface ScenarioModelType {
  id: number;
  name: string;
  type: string;
  camp?: string; // 阵营：red, blue, neutral 等
  categoryName?: string; // 类别名称（优先使用此字段进行分类）
  category?: string;
  description?: string;
  params?: ScenarioModelParam[]; // 模型参数数组，包含 side 等参数
  properties?: Record<string, any>;
  [key: string]: any;
}
