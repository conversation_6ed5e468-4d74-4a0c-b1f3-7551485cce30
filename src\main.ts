/*
 * @Author: 老范
 * @Date: 2023-02-24 09:53:51
 * @LastEditors: 老范
 * @LastEditTime: 2025-05-21 15:48:49
 * @Description: 请填写简介
 */
import {createApp} from 'vue';
import '@/style.css';
import App from '@/App.vue';
import router from '@/router/index';
import {store} from '@/pinia/index';
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import 'element-plus/dist/index.css';
import '@/assets/iconfont/iconfont.css';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import '@/assets/css/main.css';
import '@/assets/css/element_ui.less';

import Unity from 'vue-unity-webgl';
const app = createApp(App);
// 挂载element-icon
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

window.addEventListener(
  'message',
  e => {
    if (e.data?.token) {
      app.use(router);
      app.use(store);
      app.use(Unity);

      app.use(ElementPlus, {locale: zhCn});
      app.mount('#app');
    }
  },
  false
);
//开发时
app.use(router);
app.use(store);
app.use(ElementPlus, {locale: zhCn});
app.mount('#app');
