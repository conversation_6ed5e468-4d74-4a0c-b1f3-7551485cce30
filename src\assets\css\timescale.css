@font-face {
  font-family: iconfont;
  src: url('../font/iconfont.eot?t=1557382245152');
  src: url('../font/iconfont.eot?t=1557382245152#iefix') format('embedded-opentype'),
    url('data:application/x-font-woff2;charset=utf-8;base64,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')
      format('woff2'),
    url('../font/iconfont.woff?t=1557382245152') format('woff'), url('../font/iconfont.ttf?t=1557382245152') format('truetype'), url('../font/iconfont.svg?t=1557382245152#iconfont') format('svg');
}

.iconfont {
  font-family: iconfont !important;
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-style: normal;
}

.icon-timescale-bofang::before {
  content: '\e6fef';
}

.icon-timescale-baocun::before {
  content: '\e67ef';
}

.icon-timescale-fangda::before {
  content: '\e65df';
}

.icon-timescale-delete::before {
  content: '\e600f';
}

.icon-timescale-I::before {
  content: '\e66ff';
}

.icon-timescale-zanting::before {
  content: '\e611f';
}

.icon-timescale-quxiao::before {
  content: '\e701f';
}

.icon-timescale-suoxiao::before {
  content: '\e61af';
}

.icon-timescale-yulan::before {
  content: '\e63df';
}

.icon-timescale-jianqie::before {
  content: '\e618f';
}

.icon-timescale-chudian::before {
  content: '\e695f';
}

.icon-timescale-rudian::before {
  content: '\e697f';
}

.icon-timescale-lingcun::before {
  content: '\e601f';
}

#timescale-timescale {
  width: 1989px;
}

.timescale {
  /* min-height: 60px; */
  overflow: hidden;
  border: 1px solid #097db1;
  border-radius: 5px;
  box-sizing: border-box;
}

.timescale-operation * {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

.timescale-operation {
  display: none;
  height: 20px;
  overflow: hidden;
  font-size: 0;
  line-height: 20px;
  text-align: right;
  background: #141f39;
}

.timescale-operation .iconfont {
  font-size: 24px;
  color: #097db1;
  user-select: none;
}

.timescale-operation .timescale-timeshow {
  float: left;

  /* height: 100%; */
  margin-left: 10px;
}

.timescale-operation .timescale-timeshow > span {
  display: inline-block;
  margin-right: 10px;
  font-size: 14px;
  color: #fff;
}

.timescale-operation .timescale-timeshow .current-time,
.timescale-operation .timescale-timeshow .total-time {
  padding: 3px 8px;
  margin-left: 5px;
  border: 1px solid #097db1;
  border-radius: 3px;
  box-shadow: 0 0 5px 1px #097db1 inset;
}

.timescale-operation .timescale-operation-group {
  display: inline-block;
  border-left: 1px dashed #097db1;
  height: 100%;
}

.timescale-operation .timescale-operation-group span {
  display: inline-block;
  height: 100%;
  padding: 0 15px;
  cursor: pointer;
}

.timescale-operation .timescale-operation-group span:hover {
  box-shadow: 0 0 10px 1px #097db1 inset;
}

#timescale-main {
  height: 40px;

  /* border-top: 1px solid #097db1; */

  /* border-bottom: 1px solid #097db1; */
}

#timescale-scroll {
  position: relative;
  height: 20px;
}

#timescale-scroll-bar {
  position: absolute;
  top: 50%;
  z-index: 100;
  width: 100%;
  height: 6px;
  background: #097db1;
  border-radius: 3px;
  cursor: pointer;
  transform: translateY(-50%);
}

.timescale .timescale-scroll-draggerRail {
  position: absolute;
  top: 50%;
  z-index: 1;
  width: 100%;
  height: 6px;
  background: #000;
  background-color: rgb(0 0 0 / 40%);
  transform: translateY(-50%);
}

.timescale .timescale-scroll-mouse-zoom {
  position: absolute;
  top: 50%;
  z-index: 2;
  width: 100%;
  height: 14px;
  transform: translateY(-50%);
}

