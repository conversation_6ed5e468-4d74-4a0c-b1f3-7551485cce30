<script setup lang="ts">
import {ref, onBeforeUnmount, PropType} from 'vue';
type StageType = {
  minPos: number;
  maxPos: number;
  minVal: number;
  maxVal: number;
};
const emits = defineEmits(['change']);
const props = defineProps({
  // 分段数量
  stageCount: {
    type: Number,
    default: () => {
      return 8;
    },
  },
  // 每段刻度数量
  scaleCount: {
    type: Number,
    default: () => {
      return 5;
    },
  },
  // 位置 0-100
  defaultPosition: {
    type: Number,
    default: () => {
      return 25;
    },
  },
  // 默认倍速值
  defaultSpeed: {
    type: Number,
    default: () => {
      return 1;
    },
  },
  // 阶段的区间信息
  stages: {
    type: Array as PropType<StageType[]>,
    default: () => {
      return [
        {minPos: 0, maxPos: 25, minVal: 0.1, maxVal: 1.0}, // 第一阶段
        {minPos: 25, maxPos: 50, minVal: 1.0, maxVal: 10.0}, // 第一阶段
        {minPos: 50, maxPos: 75, minVal: 10.0, maxVal: 100}, // 第二阶段
        {minPos: 75, maxPos: 100, minVal: 100, maxVal: 1000}, // 第三阶段
      ];
    },
  },
  // 可重置的倍速
  resetSpeedValue: {
    type: Number,
    default: () => {
      return 1;
    },
  },
});
const sliderPostion = ref(props.defaultPosition);
const speed = ref(props.defaultSpeed);
const dragging = ref(false);
const blockRef = ref();
const startX = ref(0);
const offsetX = ref(0);
const stripRef = ref();
const speedConfigBoxRef = ref();
// 在鼠标按下时，开始拖动
const mousedownChange = (e: MouseEvent) => {
  e.preventDefault();
  document.addEventListener('mousemove', mousemoveChange); // 鼠标移动时更新滑块位置
  document.addEventListener('mouseup', mouseupChange); // 鼠标松开时停止拖动
  dragging.value = true;
  startX.value = e.clientX;
  offsetX.value = blockRef.value.offsetLeft;
  blockRef.value.style.cursor = 'grabbing'; // 设置为抓取手势
};

// 在鼠标移动时，更新滑块的位置
const mousemoveChange = (e: MouseEvent) => {
  if (!dragging.value) return;
  const stripRect = stripRef.value.getBoundingClientRect();
  let newX = e.clientX - stripRect.left; // 计算鼠标相对于进度条的位置
  newX = Math.min(Math.max(newX, 0), stripRect.width); // 限制新位置在进度条范围内
  sliderPostion.value = (newX / stripRect.width) * 100; // 计算滑块的百分比
  speed.value = Number(calculateSliderValue(sliderPostion.value)); // 更新倍数值
};
// 在鼠标点击时，更新滑块的位置
const mouseClickChange = (e: MouseEvent) => {
  dragging.value = true;
  const stripRect = stripRef.value.getBoundingClientRect(); // 获取进度条的边界
  const pos = (e.offsetX / stripRect.width) * 100;
  if (pos > sliderPostion.value) {
    speed.value = Math.min(speed.value * 10, props.stages[props.stages.length - 1].maxVal);
  } else if (pos < sliderPostion.value) {
    speed.value = Math.max(speed.value / 10, props.stages[0].minVal);
  }
  // 处理精度丢失
  speed.value = Number(speed.value.toFixed(2));
  sliderPostion.value = calculateSliderPosition(speed.value);
  emits('change', speed.value);
  // sliderPostion.value = (e.offsetX / stripRect.width) * 100; // 计算滑块的百分比位置
  // speed.value = calculateSliderValue(sliderPostion.value); // 更新倍数值
};
// 在鼠标松开时，停止拖动
const mouseupChange = () => {
  if (dragging.value) {
    blockRef.value.style.cursor = 'grab'; // 恢复为正常手势
    document.removeEventListener('mousemove', mousemoveChange);
    document.removeEventListener('mouseup', mouseupChange);
    emits('change', speed.value);
  }
};

// 根据滑块的位置计算倍数
const calculateSliderValue = (position: number) => {
  // 遍历每个阶段，根据位置所在的区间计算对应的值
  for (const stage of props.stages) {
    if (position >= stage.minPos && position <= stage.maxPos) {
      // 0.1-1 保留1位小数, 其余不保留小数
      const fixed = stage.minVal === 0.1 && stage.maxVal === 1;
      return Number((stage.minVal + ((position - stage.minPos) / (stage.maxPos - stage.minPos)) * (stage.maxVal - stage.minVal)).toFixed(fixed ? 1 : 0));
    }
  }
  return 0;
};

// 根据倍数值计算滑块的位置
const calculateSliderPosition = (val: number) => {
  // 遍历每个阶段，根据值所在的区间计算对应的位置
  for (const stage of props.stages) {
    if (val >= stage.minVal && val <= stage.maxVal) {
      return stage.minPos + ((val - stage.minVal) / (stage.maxVal - stage.minVal)) * (stage.maxPos - stage.minPos);
    }
  }
  return 0;
};

// 输入框值变化时，更新倍数和滑块位置
const speedInputChange = (value: number) => {
  speed.value = Number(Math.min(Math.max(value, 0.1), 1000).toFixed(value > 1 ? 0 : 1));
  sliderPostion.value = calculateSliderPosition(speed.value);
  emits('change', speed.value);
};
// 重置倍速
const handleSetSpeed = (drag: boolean, val = 1) => {
  dragging.value = drag;
  speed.value = val;
  sliderPostion.value = calculateSliderPosition(speed.value);
  if (drag) emits('change', speed.value);
};
// 组件卸载时移除事件
onBeforeUnmount(() => {
  document.removeEventListener('mousemove', mousemoveChange);
  document.removeEventListener('mouseup', mouseupChange);
});
defineExpose({
  handleSetSpeed,
  sliderPostion,
  speed,
  calculateSliderPosition,
  getDom: () => speedConfigBoxRef.value,
  dragging,
});
</script>

<template>
  <div ref="speedConfigBoxRef" class="dis-flex speedConfigBox">
    <el-input v-model="speed" class="speedInput" type="number" @change="speedInputChange" @focus="dragging = true" @keyup.enter="dragging = true">
      <template #prefix>
        <span>x</span>
      </template>
      <template #suffix>
        <img v-if="speed !== props.resetSpeedValue" class="pointer" src="/images/speed_reset_icon.png" alt="" @click.stop="handleSetSpeed(true, props.resetSpeedValue)" />
      </template>
    </el-input>
    <div class="box mar_l_10">
      <div ref="stripRef" class="strip" @click.stop="mouseClickChange">
        <div ref="blockRef" class="block" :style="{left: `calc(${sliderPostion}% - 6px)`}" @click.stop @mousedown="mousedownChange"></div>
        <div class="current-progress" :style="{width: `calc(${sliderPostion}% - 6px)`}"></div>
      </div>
      <el-row class="rulerBox flex-x-between flex-y-end">
        <div v-for="(item, index) in props.stageCount * props.scaleCount + 1" :key="item" :style="{height: !index || (index + 1) % props.scaleCount === 1 ? '10px' : '5px'}" class="item"></div>
      </el-row>
      <el-row class="textBox flex-x-between">
        <span>Slow</span>
        <span>Fast</span>
      </el-row>
    </div>
  </div>
</template>
<style>
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  appearance: none !important;
  margin: 0;
}

input[type='number'] {
  appearance: textfield;
}
</style>
<style lang="less" scoped>
.speedConfigBox {
  padding: 10px 7px;
  background: #004368;
  border: 1px solid #097db1;
  border-radius: 2px;

  .speedInput {
    width: 75px;
    height: 24px;

    --el-input-icon-color: var(--font-color-white);

    :deep(.el-input__wrapper) {
      padding: 1px 3px;
      border-radius: 2px;

      .el-input__prefix {
        .el-input__prefix-inner > :last-child {
          margin-right: 0;
        }
      }
    }
  }

  .box {
    width: 175px;

    .strip {
      position: relative;
      width: 100%;
      height: 6px;
      background-color: #0c7cc7a9;
      border-radius: 2px;
      cursor: pointer;

      .block {
        position: absolute;
        top: -3px;
        width: 12px;
        height: 12px;
        background: #2a74c1;
        border: 1px solid #409eff;
        border-radius: 2px;
        cursor: pointer;
      }

      .current-progress {
        position: absolute;
        top: 0;
        left: 0;
        height: 6px;
        background: #409eff;
        border-top-left-radius: 2px;
        border-bottom-left-radius: 2px;
      }
    }

    .rulerBox {
      margin-top: 7px;

      .item {
        width: 1px;
        background: #bcbcbe;
        border-radius: 2px;
      }
    }

    .textBox {
      margin-top: 5px;
      font-size: 8px;
      color: var(--font-color-white);
    }
  }
}
</style>
