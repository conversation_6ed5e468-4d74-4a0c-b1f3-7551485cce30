/*
 * @Author: 老范
 * @Date: 2023-10-26 13:20:01
 * @LastEditors: 老范
 * @LastEditTime: 2023-10-31 09:39:46
 * @Description: 用户信息全局状态管理
 */

import {ref, watch} from 'vue';
import {defineStore} from 'pinia';
import router from '@/router/index';
import {loginApi, signInApi} from '@/api/user';
interface loginFormDataType {
  username: string;
  password: string;
  code: number | string;
  uuid: string;
}
interface signinFormDataType {
  username: string;
  password: string;
  rePassword: string;
  code: number | string;
  uuid: string;
}
interface editPsdType {
  oldPassword: string;
  newPassword: string;
}
interface permissionType {
  code: number;
  data: any;
}
export const useUserStore = defineStore('user', () => {
  const token = ref(window.sessionStorage.getItem('token') || '');
  const setToken = (val: string) => {
    token.value = val;
  };
  /* 登录*/
  const LoginIn = async (loginInfo: loginFormDataType) => {
    const res: permissionType = await loginApi(loginInfo);
    console.log('🚀 ~ file: user.ts:43 ~ LoginIn ~ res:', res);
    if (res.code === 200) {
      setToken(res.data.userIdentifier);
      if (res.data.userIdentifier === 'admin') {
        router.replace({path: '/Index'});
      } else {
        router.replace({path: '/Unity'});
      }
    }
  };
  /* 注册*/
  const SignIn = async (signInInfo: signinFormDataType) => {
    const res: permissionType = await signInApi(signInInfo);
    console.log('res', res);
    if (res.code === 200) {
      // setToken(res.token);
      return true;
    }
  };
  // // 修改密码
  // const ChangePassWord = async (info: editPsdType) => {
  //   const res: permissionType = await changePassWordApi(info);
  //   if (res.code === 200) {
  //     ElMessage.success('修改成功,将重新登录!');
  //     setTimeout(() => {
  //       token.value = '';
  //       sessionStorage.clear();
  //       window.location.reload();
  //     }, 1000);
  //   }
  // };
  watch(token, () => {
    window.sessionStorage.setItem('token', token.value);
  });
  return {
    token,
    LoginIn,
    SignIn,
  };
});
