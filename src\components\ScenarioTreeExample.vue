<!--
 * @Description: 想定模型树数据示例组件
 * @Author: AI Assistant
 * @Date: 2025-06-20
 * @LastEditors: AI Assistant
 * @LastEditTime: 2025-06-20
-->
<template>
  <div class="scenario-tree-example">
    <div class="header">
      <h3>想定模型树数据示例</h3>
      <div class="controls">
        <el-button @click="loadTestData" type="primary">加载测试数据</el-button>
        <el-button @click="clearData">清空数据</el-button>
        <el-button @click="refreshTree">刷新树结构</el-button>
      </div>
    </div>

    <div class="content">
      <div class="left-panel">
        <h4>原始模型数据</h4>
        <div class="model-list">
          <div v-if="scenarioStore.modelList.length === 0" class="empty">暂无模型数据</div>
          <div v-else>
            <div v-for="model in scenarioStore.modelList" :key="model.id" class="model-item">
              <div class="model-info">
                <strong>{{ model.name }}</strong>
                <div class="model-meta">
                  <span class="camp">{{ model.camp || '未知阵营' }}</span>
                  <span class="category">{{ model.category || model.type }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="right-panel">
        <h4>转换后的树结构</h4>
        <div class="tree-structure">
          <div v-if="scenarioStore.treeData.length === 0" class="empty">暂无树数据</div>
          <div v-else class="tree-container">
            <!-- 阵营级别 -->
            <div v-for="camp in scenarioStore.treeData" :key="camp.id" class="tree-node camp-node">
              <div class="node-content">
                <span class="node-icon">🏛️</span>
                <span class="node-name">{{ camp.name }}</span>
                <span class="node-count">({{ getTotalModelsInCamp(camp) }})</span>
              </div>

              <!-- 类别级别 -->
              <div v-if="camp.children" class="children">
                <div v-for="category in camp.children" :key="category.id" class="tree-node category-node">
                  <div class="node-content">
                    <span class="node-icon">📁</span>
                    <span class="node-name">{{ category.name }}</span>
                    <span class="node-count">({{ category.children?.length || 0 }})</span>
                  </div>

                  <!-- 模型级别 -->
                  <div v-if="category.children" class="children">
                    <div v-for="model in category.children" :key="model.id" class="tree-node model-node">
                      <div class="node-content">
                        <span class="node-icon">🚀</span>
                        <span class="node-name">{{ model.name }}</span>
                        <span class="node-type">{{ model.type }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="stats">
      <div class="stat-item">
        <span class="label">模型总数:</span>
        <span class="value">{{ scenarioStore.modelCount }}</span>
      </div>
      <div class="stat-item">
        <span class="label">阵营数量:</span>
        <span class="value">{{ scenarioStore.treeData.length }}</span>
      </div>
      <div class="stat-item">
        <span class="label">类别数量:</span>
        <span class="value">{{ getTotalCategories() }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ElMessage} from 'element-plus';
import {useScenarioStore} from '@/pinia/scenario';
import type {ScenarioModelType} from '@/types/index';

const scenarioStore = useScenarioStore();

/**
 * 加载测试数据
 */
const loadTestData = () => {
  const testModels: ScenarioModelType[] = [
    // 红方飞机
    {
      id: 1,
      name: '歼-20战斗机',
      type: 'aircraft',
      categoryName: '战斗机',
      description: '第五代隐身战斗机',
      params: [
        {name: 'side', value: 'red'},
        {name: 'speed', value: 2100},
        {name: 'range', value: 2000},
      ],
    },
    {
      id: 2,
      name: '歼-16战斗机',
      type: 'aircraft',
      categoryName: '战斗机',
      description: '多用途战斗机',
      params: [
        {name: 'side', value: 'red'},
        {name: 'speed', value: 1800},
        {name: 'range', value: 1500},
      ],
    },
    {
      id: 3,
      name: '轰-6K轰炸机',
      type: 'aircraft',
      categoryName: '轰炸机',
      description: '远程轰炸机',
      params: [
        {name: 'side', value: 'red'},
        {name: 'speed', value: 1050},
        {name: 'range', value: 3500},
      ],
    },

    // 红方舰船
    {
      id: 4,
      name: '055型驱逐舰',
      type: 'ship',
      categoryName: '驱逐舰',
      description: '大型导弹驱逐舰',
      params: [
        {name: 'side', value: 'red'},
        {name: 'displacement', value: 12000},
        {name: 'speed', value: 30},
      ],
    },
    {
      id: 5,
      name: '054A型护卫舰',
      type: 'ship',
      categoryName: '护卫舰',
      description: '多用途护卫舰',
      params: [
        {name: 'side', value: 'red'},
        {name: 'displacement', value: 4000},
        {name: 'speed', value: 27},
      ],
    },

    // 红方导弹
    {
      id: 6,
      name: '鹰击-18反舰导弹',
      type: 'missile',
      categoryName: '反舰导弹',
      description: '超音速反舰导弹',
      params: [
        {name: 'side', value: 'red'},
        {name: 'range', value: 540},
        {name: 'speed', value: 3},
      ],
    },
    {
      id: 7,
      name: '红旗-9防空导弹',
      type: 'missile',
      categoryName: '防空导弹',
      description: '远程防空导弹',
      params: [
        {name: 'side', value: 'red'},
        {name: 'range', value: 200},
        {name: 'altitude', value: 30000},
      ],
    },

    // 蓝方飞机
    {
      id: 8,
      name: 'F-22战斗机',
      type: 'aircraft',
      categoryName: '战斗机',
      description: '第五代隐身战斗机',
      params: [
        {name: 'side', value: 'blue'},
        {name: 'speed', value: 2410},
        {name: 'range', value: 2960},
      ],
    },
    {
      id: 9,
      name: 'F-35战斗机',
      type: 'aircraft',
      categoryName: '战斗机',
      description: '多用途隐身战斗机',
      params: [
        {name: 'side', value: 'blue'},
        {name: 'speed', value: 1930},
        {name: 'range', value: 2220},
      ],
    },
    {
      id: 10,
      name: 'B-2轰炸机',
      type: 'aircraft',
      categoryName: '轰炸机',
      description: '隐身战略轰炸机',
      params: [
        {name: 'side', value: 'blue'},
        {name: 'speed', value: 1010},
        {name: 'range', value: 11100},
      ],
    },

    // 蓝方舰船
    {
      id: 11,
      name: '阿利伯克级驱逐舰',
      type: 'ship',
      categoryName: '驱逐舰',
      description: '导弹驱逐舰',
      params: [
        {name: 'side', value: 'blue'},
        {name: 'displacement', value: 9200},
        {name: 'speed', value: 30},
      ],
    },
    {
      id: 12,
      name: '提康德罗加级巡洋舰',
      type: 'ship',
      categoryName: '巡洋舰',
      description: '导弹巡洋舰',
      params: [
        {name: 'side', value: 'blue'},
        {name: 'displacement', value: 9600},
        {name: 'speed', value: 32},
      ],
    },

    // 蓝方导弹
    {
      id: 13,
      name: '鱼叉反舰导弹',
      type: 'missile',
      categoryName: '反舰导弹',
      description: '亚音速反舰导弹',
      params: [
        {name: 'side', value: 'blue'},
        {name: 'range', value: 280},
        {name: 'speed', value: 0.85},
      ],
    },
    {
      id: 14,
      name: '标准-6防空导弹',
      type: 'missile',
      categoryName: '防空导弹',
      description: '多用途防空导弹',
      params: [
        {name: 'side', value: 'blue'},
        {name: 'range', value: 240},
        {name: 'altitude', value: 33000},
      ],
    },

    // 中立/其他（没有 side 参数，应该归类为 neutral）
    {
      id: 15,
      name: '民用客机',
      type: 'aircraft',
      categoryName: '运输机',
      description: '民用航空器',
      params: [
        {name: 'passengers', value: 300},
        {name: 'speed', value: 900},
      ],
    },
    {
      id: 16,
      name: '货轮',
      type: 'ship',
      categoryName: '货船',
      description: '民用货船',
      params: [
        {name: 'cargo_capacity', value: 20000},
        {name: 'speed', value: 15},
      ],
    },
  ];

  scenarioStore.setModelList(testModels);
  ElMessage.success('测试数据加载成功');
};

/**
 * 清空数据
 */
const clearData = () => {
  scenarioStore.clearAll();
  ElMessage.info('数据已清空');
};

/**
 * 刷新树结构
 */
const refreshTree = () => {
  scenarioStore.convertModelsToTreeData();
  ElMessage.success('树结构已刷新');
};

/**
 * 获取阵营中的模型总数
 */
const getTotalModelsInCamp = (camp: {children?: {children?: any[]}[]}): number => {
  if (!camp.children) return 0;
  return camp.children.reduce((total: number, category: {children?: any[]}) => {
    return total + (category.children?.length || 0);
  }, 0);
};

/**
 * 获取总类别数量
 */
const getTotalCategories = (): number => {
  return scenarioStore.treeData.reduce((total: number, camp: {children?: any[]}) => {
    return total + (camp.children?.length || 0);
  }, 0);
};
</script>

<style scoped>
.scenario-tree-example {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.header h3 {
  margin: 0;
  color: #333;
}

.controls {
  display: flex;
  gap: 8px;
}

.content {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.left-panel,
.right-panel {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 16px;
}

.left-panel h4,
.right-panel h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.model-list {
  max-height: 400px;
  overflow-y: auto;
}

.model-item {
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  margin-bottom: 8px;
}

.model-info strong {
  display: block;
  color: #333;
  margin-bottom: 4px;
}

.model-meta {
  display: flex;
  gap: 8px;
}

.camp,
.category {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  background-color: #f0f0f0;
  color: #666;
}

.tree-structure {
  max-height: 400px;
  overflow-y: auto;
}

.tree-node {
  margin-bottom: 8px;
}

.node-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  border-radius: 4px;
  background-color: #f8f9fa;
}

.camp-node .node-content {
  background-color: #e3f2fd;
  font-weight: 600;
}

.category-node .node-content {
  background-color: #f3e5f5;
  margin-left: 20px;
}

.model-node .node-content {
  background-color: #fff3e0;
  margin-left: 40px;
}

.node-icon {
  font-size: 16px;
}

.node-name {
  flex: 1;
  color: #333;
}

.node-count,
.node-type {
  font-size: 12px;
  color: #666;
}

.children {
  margin-top: 8px;
}

.empty {
  text-align: center;
  color: #999;
  padding: 40px;
  font-style: italic;
}

.stats {
  display: flex;
  gap: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  color: #666;
  font-size: 14px;
}

.value {
  color: #333;
  font-weight: 600;
  font-size: 16px;
}
</style>

