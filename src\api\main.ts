/*
 * @Author: 老范
 * @Date: 2023-05-10 11:17:19
 * @LastEditors: 老范
 * @LastEditTime: 2023-09-11 15:45:23
 * @Description: 请填写简介
 */
import http from '@/utils/http';
import service from '@/utils/request';

/**
 * @description:获取xxx信息
 * @param {Object} params
 * @return {*}
 */
export const createFnTest = (params: Object) => {
  return http.post(`/api/v1/demo`, params);
};
/**
 * @description:获取xxx信息
 * @param {Object} params
 * @return {*}
 */
export const getFn = (id: string | number) => {
  return http.get(`/api/xx/${id}`);
};

/**
 * @description:新增xxx信息
 * @param {Object} params
 * @return {*}
 */
export const postFn = (params: Object) => {
  return http.post(`/api/xx/xx`, params);
};
/**
 * @description:更新xxx信息
 * @param {Object} params
 * @return {*}
 */
export const putFn = (id: string | number, params: Object) => {
  return http.put(`/api/xx/${id}`, params);
};
/**
 * @description:删除
 * @param {Object} params
 * @return {*}
 */
export const deleteFn = (params: Object) => {
  return http.delete(`/api/xx/xx`, params);
};
export const getControlerStatus = () => {
  return service({
    url: `/status`,
    method: 'get',
    urlType: 'api',
  });
};
