<!--
 * @Description: 请填写简介
 * @Author: tzs
 * @Date: 2024-09-11 11:46:03
 * @LastEditors: liukun
 * @LastEditTime: 2024-12-26 14:40:26
-->
<script setup lang="ts">
import {ref, watch, nextTick} from 'vue';
import {ElMessage, ElMessageBox} from 'element-plus';
import moment from 'moment';
import {deleteKeepFileById, deleteKeepFileList, getKeepFileById, getListApi, turnToKeepFile} from '@/api/simulation';
import {ScenarioType, FileKeepType, ArchiveType} from '@/types/index';
const emits = defineEmits(['receiveData', 'confimInit', 'changeRefresh']);
const list = ref<ScenarioType[]>([]);
const fileKeepList = ref<FileKeepType[]>([]);
// 分页总数
const total = ref(0);
const dialogVisible = ref(false);
// 选中某一行的数据
const scenarioData = ref<ScenarioType>({} as ScenarioType);
const scenarioTableRef = ref();
const activeCollapse = ref<number[]>([]);
const currentFileKeep = ref({} as ScenarioType);
// 获取想定列表的参数
const listQuery = ref({
  s: '',
  pageIndex: 1,
  pageSize: 10,
});

watch(
  () => dialogVisible.value,
  async newV => {
    if (!newV) return;
    await getList();
    if (!scenarioData.value?.id) return;
    nextTick(() => {
      const row = list.value.find(i => i.id === scenarioData.value.id);
      row && scenarioTableRef.value.setCurrentRow(row);
    });
  }
);
const getList = async () => {
  const res = (await getListApi(listQuery.value)).data;
  list.value = res.list;
  total.value = res.count;
  if (!scenarioData.value?.id) return;
  nextTick(() => {
    const row = list.value.find(i => i.id === scenarioData.value.id);
    if (row) {
      scenarioTableRef.value.setCurrentRow(row);
    } else {
      scenarioData.value = {} as ScenarioType;
      activeCollapse.value = [];
    }
  });
  return;
};
// 选择表格某一行数据
const selectTableRow = async (row: ScenarioType) => {
  if (!row) return;

  scenarioData.value = row;
};
// 选择存档管理
const handleRecordManage = async (scenarioId: number) => {
  const res = await getKeepFileById({scenarioId: scenarioId});
  fileKeepList.value = res.data.list || [];
  const scenario = list.value.find(i => i.id === scenarioId);
  scenario && (currentFileKeep.value = scenario);
};
// 断点重启
const breakpointRestart = async (row: ArchiveType, item: FileKeepType) => {
  await turnToKeepFile({id: item.scenarioId, timeStamp: row.timeStamp, mode: row.deductionMode, speedRatio: row.speed, step: row.step});
  emits('changeRefresh');
  dialogVisible.value = false;
};
// 删除某一次推演存档
const deleteRecordList = (row: FileKeepType) => {
  ElMessageBox.confirm('确定要删除吗?', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await deleteKeepFileList({ids: [row.id]});
    ElMessage.success('删除成功');
    handleRecordManage(row.scenarioId);
  });
};
// 删除某一断点存档数据
const deleteRecord = (row: ArchiveType, item: FileKeepType) => {
  ElMessageBox.confirm('确定要删除吗?', '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    await deleteKeepFileById({ids: [row.id]});
    ElMessage.success('删除成功');
    if (item.archives.length === 1) {
      await deleteKeepFileList({ids: [item.id]});
    }
    handleRecordManage(item.scenarioId);
  });
};
// 确认选中想定
const confim = async () => {
  if (!scenarioData.value.id) return ElMessage.error('请选择想定数据');
  emits('receiveData', scenarioData.value);
  dialogVisible.value = false;
};

const handleCurrentChange = (val: number) => {
  listQuery.value.pageIndex = val;
  fileKeepList.value = [];
  currentFileKeep.value = {} as ScenarioType;
  scenarioData.value = {} as ScenarioType;
  activeCollapse.value = [];
  getList();
};
const searchList = () => {
  listQuery.value.pageIndex = 1;
  getList();
};
// 双击
const handleDblTableRow = () => {
  ElMessageBox.confirm('是否初始化想定?', '想定初始化', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    emits('confimInit', scenarioData.value);
    dialogVisible.value = false;
  });
};
defineExpose({
  dialogVisible,
});
</script>

<template>
  <el-dialog v-model="dialogVisible" title="选取想定" width="1080px" draggable :close-on-click-modal="false" :close-on-press-escape="false" destroy-on-close :header-row-style="{background: '#084669;'}" align-center>
    <div class="search-box">
      <el-input v-model="listQuery.s" clearable placeholder="请输入ID/名称" @keyup.enter="searchList" @clear="searchList"
        ><template #suffix>
          <el-icon class="pointer"><Search @click.stop="searchList" /></el-icon>
        </template>
      </el-input>
    </div>
    <el-table ref="scenarioTableRef" :data="list" show-overflow-tooltip class="scenario-table" stripe :height="300" highlight-current-row :row-style="{height: '20px'}" @current-change="selectTableRow" @row-dblclick="handleDblTableRow">
      <el-table-column label="想定ID" prop="id" align="center" min-width="50"> </el-table-column>
      <el-table-column label="仿真想定名称" prop="name" align="center" min-width="100"></el-table-column>
      <el-table-column label="仿真想定描述" prop="description" align="center" min-width="100"> </el-table-column>
      <el-table-column label="创建时间" prop="createTime" align="center" min-width="100"> </el-table-column>
      <el-table-column label="更新时间" prop="updateTime" align="center" min-width="100"> </el-table-column>
      <el-table-column label="操作" align="center" min-width="100">
        <template #default="{row}">
          <el-row class="flex-x-center">
            <el-row class="align-items pointer" @click="handleRecordManage(row.id)">
              <img src="/images/table_record_icon.png" alt="" class="mar_r_5" />
              <span :style="{color: '#36b6ff'}" class="mar_r_10">存档管理</span>
            </el-row>
          </el-row>
        </template>
      </el-table-column>
    </el-table>
    <div class="width_100 dis-flex flex-x-center pad_v_10">
      <el-pagination :current-page="listQuery.pageIndex" :page-size="listQuery.pageSize" layout=" prev, pager, next" :total="total" @current-change="handleCurrentChange"></el-pagination>
    </div>
    <div class="file-manage">
      <div class="title">
        <span>存档管理</span>
        <span v-if="currentFileKeep.name" :style="{color: '#36b6ff'}">（{{ currentFileKeep.name }}）</span>
      </div>
      <div class="content">
        <el-row class="header">
          <div class="occupation"></div>
          <el-table :data="[]" class="table-title">
            <el-table-column label="存档时间(仿真时间)" :min-width="140" align="center" />
            <el-table-column label="推演模式" :min-width="100" align="center" />
            <el-table-column label="仿真步长" :min-width="80" align="center" />
            <el-table-column label="仿真倍率" :min-width="80" align="center" />
            <el-table-column label="存档模式" :min-width="80" align="center" />
            <el-table-column label="存档描述" :min-width="100" align="center" />
            <el-table-column label="操作" :min-width="140" align="center" />
          </el-table>
        </el-row>
        <el-collapse v-if="fileKeepList.length" v-model="activeCollapse" :style="{height: '300px'}">
          <template v-for="item in fileKeepList" :key="item.id">
            <el-collapse-item :name="item.id">
              <template #title>
                <el-row class="collapse-title width_100 align-items flex-x-between">
                  <el-row class="align-items">
                    <span class="lable mar_r_15">推演时间(天文时间)</span>
                    <template v-if="!activeCollapse.includes(item.id)">
                      <span class="mar_r_15">{{ item.startTime }}</span>
                      <span class="divider mar_r_15"></span>
                      <span class="mar_r_15">存档总数：{{ item.total }}条</span>
                    </template>
                  </el-row>
                  <el-row class="align-items">
                    <el-icon class="header-icon mar_r_10">
                      <CaretRight />
                    </el-icon>
                    <img src="/images/table_delete_icon.png" alt="" class="mar_r_5" title="删除" @click.stop="deleteRecordList(item)" />
                  </el-row>
                </el-row>
              </template>
              <el-row v-if="item.archives.length" class="width_100">
                <el-row class="simTime-label flex-dir-column flex-x-center">
                  <p>{{ moment(item.startTime, 'YYYY-MM-DD HH:mm:ss').format('YYYY') || '' }}</p>
                  <p>{{ moment(item.startTime, 'YYYY-MM-DD HH:mm:ss').format('MM-DD') || '' }}</p>
                  <p>{{ moment(item.startTime, 'YYYY-MM-DD HH:mm:ss').format('HH:mm:ss') || '' }}</p>
                  <p class="divider"></p>
                  <p>{{ item.total }}条</p>
                </el-row>
                <el-table :data="item.archives" :show-overflow-tooltip="true" height="144px" class="collapse-el-table" stripe>
                  <el-table-column prop="simTime" align="center" :min-width="140"> </el-table-column>
                  <el-table-column align="center" :min-width="100">
                    <template #default="scope">
                      {{ scope.row.deductionMode ? '帧模式' : '事件模式' }}
                    </template>
                  </el-table-column>
                  <el-table-column align="center" :min-width="80">
                    <template #default="scope">
                      {{ scope.row.deductionMode ? scope.row.step : '--' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="speed" align="center" :min-width="80" />
                  <el-table-column align="center" :min-width="80">
                    <template #default="scope">
                      {{ scope.row.automatic == false ? '手动' : '自动' }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="description" align="center" :min-width="100" />
                  <el-table-column align="center" :min-width="140">
                    <template #default="scope">
                      <el-row class="flex-x-center">
                        <el-row class="align-items pointer" @click.stop="deleteRecord(scope.row, item)">
                          <img src="/images/table_delete_icon.png" alt="" class="mar_r_5" />
                          <span :style="{color: '#36b6ff'}" class="mar_r_10">删除</span>
                        </el-row>
                        <el-row class="align-items pointer" @click.stop="breakpointRestart(scope.row, item)"
                          ><img src="/images/table_reset_icon.png" alt="" class="mar_r_5" />
                          <span :style="{color: '#36b6ff'}">断点重启</span>
                        </el-row>
                      </el-row>
                    </template>
                  </el-table-column>
                </el-table>
              </el-row>
            </el-collapse-item>
          </template>
        </el-collapse>
        <div v-else class="empty-box dis-flex flex-dir-column align-items flex-x-center">
          <img src="/images/empty_img.png" alt="" :style="{scale: '0.9'}" />
          <p class="empty-text mar_t_10">暂无更多信息</p>
        </div>
      </div>
    </div>

    <el-row class="dis-flex flex-x-center mar_b_10 mar_t_10 dialog-footer">
      <el-button @click="dialogVisible = false">取&nbsp;消</el-button>
      <el-button @click="confim">确&nbsp;认</el-button>
    </el-row>
  </el-dialog>
</template>
<style lang="less" scoped>
.search-box {
  margin-bottom: 10px !important;
}

.scenario-table {
  --el-table-tr-bg-color: #084669;
}

:deep(.el-table__cell) {
  border: 1px solid #1377b0;
  border-left: 0;
}

:deep(.el-table__cell:first-child) {
  border-left: solid 1px #0f6ca5;
}

:deep(.el-table__cell:last-child) {
  border-right: solid 1px #0f6ca5;
}

:deep(.el-table__row:last-child .el-table__cell) {
  border-bottom: 1px solid #1377b0;
}

.file-manage {
  border: solid 1px #1377b0;
  border-radius: 2px;

  .title {
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: #0c486a;
  }

  .content {
    padding: 4px 7px;

    .header {
      margin-bottom: 5px;
      background: #084669;
      border: 1px solid #36b6ff;
      border-radius: 2px;

      .occupation {
        width: 70px;
      }

      .table-title {
        width: calc(100% - 70px);

        :deep(.el-table__empty-block) {
          display: none;
        }

        :deep(.el-table__body-wrapper) {
          display: none;
        }

        :deep(.el-table__header) {
          .el-table__cell {
            height: 38px;
            border: none;

            .cell {
              color: #fff;
            }
          }
        }

        // .table-column {
        //   line-height: 36px;
        // }
      }
    }

    :deep(.el-collapse) {
      overflow: auto;
      border: unset;

      .el-collapse-item {
        padding: unset;
        margin-bottom: 5px;
        border: 1px solid #1377b0;
        border-radius: 2px;

        .el-collapse-item__header {
          .header-icon {
            font-size: 18px;
            color: var(--expand-icon-color);
            transform: rotate(0deg);
            transition: 0.3s ease-in-out;
          }

          .collapse-title {
            padding: 0 30px 0 5px;

            .lable {
              padding: 4px 6px;
              color: #fff;
              background-color: #146392;
              border-radius: 4px;
            }

            .divider {
              width: 2px;
              height: 15px;
              background-color: #36b6ff;
            }

            span {
              /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
              font-family: Regular;
            }
          }

          border-bottom: none;
          position: relative;
          height: 30px;
          font-size: 15px;
          color: #85d2ff;
          background-color: #0f5075;
          align-items: center;
        }

        .is-active .header-icon {
          transform: rotate(90deg);
          transition: 0.3s ease-in-out;
        }

        .el-collapse-item__wrap {
          padding: 0;
          background: #00273e;
          border-bottom: unset;

          .el-collapse-item__content {
            display: flex;
            padding: 0;
            flex-wrap: wrap;

            .simTime-label {
              width: 70px;
              padding-left: 4px;
              background: #093e5f;
              border-top: 1px solid #1377b0;
              border-right: 1px solid #1377b0;

              p {
                margin-bottom: 8px;
                color: #85d2ff;
              }

              .divider {
                width: 28px;
                height: 2px;
                margin: 7px 0 15px;
                background: #36b6ff;
              }
            }

            .collapse-el-table {
              width: calc(100% - 70px);

              .el-table__header-wrapper {
                display: none;
              }

              .el-table__body {
                .el-table__row {
                  font-size: 14px;
                  color: #ace1ff;
                }

                .el-table__cell {
                  height: 36px;
                }

                .el-table__cell:first-child {
                  border-left: none;
                }
              }
            }
          }
        }
      }
    }
  }
}

.handleClick {
  display: flex;
  align-items: center;
}

.empty-box {
  height: 300px;

  .empty-text {
    font-family: Normal;
    color: #999;
  }
}
</style>
