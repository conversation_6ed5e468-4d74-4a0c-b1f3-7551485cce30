/*
 * @Author: 老范
 * @Date: 2023-10-26 13:19:49
 * @LastEditors: 老范
 * @LastEditTime: 2023-10-27 13:54:31
 * @Description: 请填写简介
 */
/*
 * @Author: 老范
 * @Date: 2023-05-10 11:17:19
 * @LastEditors: liukun
 * @LastEditTime: 2023-08-15 09:20:48
 * @Description: 请填写简介
 */
import http from '@/utils/http';
/**
 * @description: 获取验证码
 * @param {object} params
 * @return {*}
 */
export const getCaptchaApi = (params: object | undefined) => {
  return http.get(`/api/v1/captcha`, params);
};

/**
 * @description: 登录接口
 * @param {*} params 账号、密码、验证码
 * @return {*}
 */
export const loginApi = (params: object | undefined) => {
  return http.get(`/api/user/login`, params);
};

/**
 * @description: 注册接口
 * @param {object} params
 * @return {*}
 */
export const signInApi = (params: object | undefined) => {
  return http.post(`/api/user`, params);
};

/**
 * @description: 修改信息
 * @param {object} params
 * @return {*}
 */
export const changeUserInfo = (id: number, params: object | undefined) => {
  return http.put(`/api/user/${id}`, params);
};
/**
 * @description: 查询全部用户列表
 * @param {object} params
 * @return {*}
 */
export const getUserList = (params: object | undefined) => {
  return http.get(`/api/user/list`, params);
};
/**
 * @description: 删除用户
 * @param {object} params
 * @return {*}
 */
export const delUser = (params: object | undefined) => {
  return http.delete(`/api/user/${params}`);
};
/**
 * @description: 获取人数信息
 * @param {object} params
 * @return {*}
 */
export const getUserNumber = () => {
  return http.get(`/api/info/`);
};
