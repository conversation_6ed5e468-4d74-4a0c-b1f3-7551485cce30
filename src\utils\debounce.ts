/*
 * @Author: liukun
 * @Date: 2024-12-26 11:10:02
 * @LastEditors: liukun
 * @LastEditTime: 2024-12-26 11:18:56
 * @FilePath: \afsim-situation3d\src\utils\debounce.ts
 * @Description: 防抖函数 + 立即触发
 *
 */
const debounce = <T extends (...args: any[]) => void>(func: T, wait: number) => {
  let timeout: NodeJS.Timeout;
  let isFirstClick = true;
  return (...args: Parameters<T>) => {
    // 如果是第一次点击, 立即触发
    if (isFirstClick) {
      func(...args);
      isFirstClick = false;
    }
    // 如果定时器存在, 清除定时器
    if (timeout) clearTimeout(timeout);
    // 在等待后允许下一次点击
    timeout = setTimeout(() => {
      isFirstClick = true; // 重置为可触发;
    }, wait);
  };
};
export default debounce;
