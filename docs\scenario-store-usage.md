# 想定模型列表 Pinia Store 使用指南

## 概述

`useScenarioStore` 是一个专门用于管理想定相关数据的 Pinia store，主要功能是存储和管理从 `getScenarioInfoApi` 接口返回的 `models` 字段数据。

## 功能特性

### 1. 状态管理

- **currentScenario**: 当前选中的想定信息
- **modelList**: 当前想定的模型列表
- **treeData**: 转换后的树数据结构（三级：阵营 → 类别 → 模型）
- **loading**: 加载状态
- **error**: 错误信息

### 2. 计算属性

- **modelsByType**: 按类型分组的模型
- **modelsByCategory**: 按类别分组的模型
- **modelCount**: 模型总数

### 3. 核心方法

- **fetchScenarioModels**: 获取想定信息并提取模型列表
- **setModelList**: 设置模型列表
- **convertModelsToTreeData**: 将模型列表转换为树数据结构
- **addModel**: 添加模型
- **removeModel**: 删除模型
- **updateModel**: 更新模型

## 使用方法

### 1. 基本使用

```typescript
import {useScenarioStore} from '@/pinia/scenario';

const scenarioStore = useScenarioStore();

// 获取想定模型列表
await scenarioStore.fetchScenarioModels(scenarioId);

// 访问模型数据
console.log('模型列表:', scenarioStore.modelList);
console.log('模型总数:', scenarioStore.modelCount);
console.log('按类型分组:', scenarioStore.modelsByType);
```

### 2. 在组件中使用

```vue
<template>
  <div>
    <div v-if="scenarioStore.loading">加载中...</div>
    <div v-else-if="scenarioStore.error">{{ scenarioStore.error }}</div>
    <div v-else>
      <div v-for="model in scenarioStore.modelList" :key="model.id">{{ model.name }} - {{ model.type }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {useScenarioStore} from '@/pinia/scenario';

const scenarioStore = useScenarioStore();

// 获取模型数据
const loadModels = async (scenarioId: number) => {
  try {
    await scenarioStore.fetchScenarioModels(scenarioId);
  } catch (error) {
    console.error('加载失败:', error);
  }
};
</script>
```

### 3. 模型操作

```typescript
// 添加模型
scenarioStore.addModel({
  id: 1,
  name: '新模型',
  type: 'aircraft',
  category: 'fighter',
});

// 更新模型
scenarioStore.updateModel(1, {
  name: '更新后的模型名称',
  description: '新的描述',
});

// 删除模型
scenarioStore.removeModel(1);

// 根据条件查询
const aircraftModels = scenarioStore.getModelsByType('aircraft');
const fighterModels = scenarioStore.getModelsByCategory('fighter');
```

## 数据结构

### ScenarioModelParam 接口

```typescript
export interface ScenarioModelParam {
  name: string;
  value: any;
  type?: string;
  description?: string;
}
```

### ScenarioModelType 接口

```typescript
export interface ScenarioModelType {
  id: number;
  name: string;
  type: string;
  camp?: string; // 阵营：red, blue, neutral 等
  category?: string;
  description?: string;
  params?: ScenarioModelParam[]; // 模型参数数组，包含 side 等参数
  properties?: Record<string, any>;
  [key: string]: any;
}
```

### 重要说明

**阵营识别**: 系统会从 `params` 数组中查找 `name` 为 "side" 的参数来确定模型所属阵营：

```typescript
// 示例：红方模型
{
  id: 1,
  name: '歼-20战斗机',
  params: [
    { name: 'side', value: 'red' },  // 这里定义了阵营
    { name: 'speed', value: 2100 },
    { name: 'range', value: 2000 }
  ]
}

// 示例：蓝方模型
{
  id: 2,
  name: 'F-22战斗机',
  params: [
    { name: 'side', value: 'blue' }, // 这里定义了阵营
    { name: 'speed', value: 2410 },
    { name: 'range', value: 2960 }
  ]
}
```

## API 集成

### getScenarioInfoApi 接口集成

Store 会自动调用 `getScenarioInfoApi(scenarioId)` 接口，并从返回值中提取 `models` 字段：

```typescript
// 接口返回结构示例
{
  "code": 200,
  "data": {
    "id": 1,
    "name": "想定名称",
    "models": [
      {
        "id": 1,
        "name": "F-16战斗机",
        "type": "aircraft",
        "category": "fighter",
        "description": "多用途战斗机"
      },
      // ... 更多模型
    ]
  }
}
```

## 组件示例

项目中提供了 `ScenarioModelList.vue` 组件作为使用示例，展示了：

1. 如何显示模型列表
2. 如何按类型/类别分组
3. 如何处理加载状态和错误
4. 如何实现模型选择和查看功能

## 最佳实践

### 1. 错误处理

```typescript
try {
  await scenarioStore.fetchScenarioModels(scenarioId);
} catch (error) {
  // 处理错误
  console.error('获取模型失败:', error);
  // 显示用户友好的错误信息
}
```

### 2. 加载状态管理

```vue
<template>
  <div v-loading="scenarioStore.loading">
    <!-- 内容 -->
  </div>
</template>
```

### 3. 响应式数据

```typescript
import {storeToRefs} from 'pinia';

const {modelList, modelCount, loading, error} = storeToRefs(scenarioStore);
```

## 注意事项

1. **数据格式**: 确保 API 返回的 `models` 字段是数组格式
2. **错误处理**: 始终处理可能的网络错误和数据格式错误
3. **内存管理**: 在不需要时调用 `clearAll()` 清理数据
4. **类型安全**: 使用 TypeScript 类型确保数据结构正确

## 树数据转换功能

### 自动转换

当调用 `fetchScenarioModels` 或 `setModelList` 时，Store 会自动将模型列表转换为三级树结构：

```
阵营 (Level 1)
├── 类别 (Level 2)
│   ├── 模型 (Level 3)
│   ├── 模型 (Level 3)
│   └── ...
├── 类别 (Level 2)
└── ...
```

### 阵营识别规则

1. **优先使用 `camp` 字段**: 如果模型有 `camp` 属性，直接使用
2. **从 `params` 数组提取**: 查找 `name` 为 "side" 的参数，使用其 `value` 值
3. **名称推断**: 根据模型名称包含的关键词推断
   - 包含"红"、"我方" → `red`
   - 包含"蓝"、"敌方" → `blue`
   - 其他 → `neutral`

### 类别识别规则

1. **优先使用 `categoryName` 字段**: 如果模型有 `categoryName` 属性，直接使用（推荐）
2. **使用 `category` 字段**: 如果没有 `categoryName`，使用 `category`
3. **使用 `type` 字段**: 如果都没有，使用 `type`
4. **默认值**: 如果都没有，使用 `unknown`

### 使用示例

```typescript
// 设置包含阵营信息的模型数据
const models = [
  {
    id: 1,
    name: '歼-20战斗机',
    type: 'aircraft',
    categoryName: '战斗机', // 使用 categoryName 进行分类
    params: [
      {name: 'side', value: 'red'},
      {name: 'speed', value: 2100},
      {name: 'range', value: 2000},
    ],
  },
  {
    id: 2,
    name: 'F-22战斗机',
    type: 'aircraft',
    categoryName: '战斗机', // 使用 categoryName 进行分类
    params: [
      {name: 'side', value: 'blue'},
      {name: 'speed', value: 2410},
      {name: 'range', value: 2960},
    ],
  },
  {
    id: 3,
    name: '055型驱逐舰',
    type: 'ship',
    categoryName: '驱逐舰', // 更具体的分类
    params: [
      {name: 'side', value: 'red'},
      {name: 'displacement', value: 12000},
    ],
  },
  // ...
];

scenarioStore.setModelList(models);

// 访问转换后的树数据
console.log('树结构:', scenarioStore.treeData);

// 在 nodeDesign.vue 中会自动使用这个树数据
```

### 与 nodeDesign.vue 集成

`nodeDesign.vue` 组件会自动使用 scenario store 中的 `treeData`：

```vue
<!-- nodeDesign.vue 中的树结构会显示转换后的数据 -->
<div v-for="camp in treeData" :key="camp.id" class="tree-node level-1">
  <!-- 显示阵营、类别、模型的三级结构 -->
</div>
```

## 扩展功能

Store 设计为可扩展的，可以根据需要添加：

- 模型搜索和过滤功能
- 模型排序功能
- 本地缓存机制
- 模型关系管理
- 批量操作功能
- 自定义树结构转换规则

