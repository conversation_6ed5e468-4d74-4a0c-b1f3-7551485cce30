/*
 * <AUTHOR> laofan
 * @Date         : 2023-03-06 14:17:30
 * @LastEditors: liukun
 * @LastEditTime: 2025-01-16 16:56:04
 * @Description  : 请填写简介
 */
import {defineStore} from 'pinia';
export interface controlerStatusType {
  statusList: {
    isInit: boolean;
    isStart: boolean;
    isPause: boolean;
    isContinue: boolean;
    isStop: boolean;
    isReset: boolean;
  }[];
}
const controlerStatus: controlerStatusType = {
  statusList: [
    // 0未初始化
    {
      isInit: true,
      isStart: false,
      isPause: false,
      isContinue: false,
      isStop: false,
      isReset: true,
    },
    // 1已初始化
    {
      isInit: false,
      isStart: true,
      isPause: false,
      isContinue: false,
      isStop: true,
      isReset: true,
    },
    // 2已经开始
    {
      isInit: false,
      isStart: false,
      isPause: true,
      isContinue: false,
      isStop: true,
      isReset: false,
    },
    //3 已暂停状态
    {
      isInit: false,
      isStart: false,
      isPause: false,
      isContinue: true,
      isStop: true,
      isReset: true,
    },
    // 4停止（同未初始化）
    {
      isInit: true,
      isStart: false,
      isPause: false,
      isContinue: false,
      isStop: false,
      isReset: true,
    },
    // 服务出错
    {
      isInit: true,
      isStart: false,
      isPause: false,
      isContinue: false,
      isStop: false,
      isReset: false,
    },
  ],
};

export const useControler = defineStore('controler', {
  state: () => {
    return controlerStatus;
  },
});
