/*
 * @Author: 老范
 * @Date: 2024-05-13 11:24:22
 * @LastEditors: liukun
 * @LastEditTime: 2024-12-26 11:05:33
 * @Description: timeLine
 */
(function () {
  var ready = {
      //获取当前脚步的基础路径
      getPath: (function () {
        const jsPath = document.currentScript
          ? document.currentScript.src
          : (function () {
              let js = document.scripts,
                last = js.length - 1,
                src;
              for (let i = last; i > 0; i--) {
                if (js[i].readyState === 'interactive') {
                  src = js[i].src;
                  break;
                  /**
                   * @description:
                   * @return {*}
                   */
                }
              }
              return src || js[last].src;
            })();
        return jsPath.substring(0, jsPath.lastIndexOf('/') + 1);
      })(),

      //获取节点的style属性值
      getStyle: function (node, name) {
        // var style = node.currentStyle ? node.currentStyle : window.getComputedStyle(node, null);
        // return style[style.getPropertyValue ? 'getPropertyValue' : 'getAttribute'](name);
        return window.getComputedStyle(node)[name];
      },

      //载入css配件
      link: function (href, fn, cssname) {
        //未设置路径，则不主动加载css
        if (!timescale.path) return;

        const head = document.getElementsByTagName('head')[0],
          link = document.createElement('link');
        if (typeof fn === 'string') cssname = fn;
        const app = (cssname || href).replace(/\.|\//g, '');
        let id = 'timescale-' + app,
          timeout = 0;

        link.rel = 'stylesheet';
        link.href = timescale.path + href;
        link.id = id;

        if (!document.getElementById(id)) {
          head.appendChild(link);
        }

        if (typeof fn !== 'function') return;

        //轮巡css是否加载完毕
        (function poll() {
          if (++timeout > (8 * 1000) / 100) {
            return window.console || console.error('timescale.css: Invalid');
          }
          parseInt(ready.getStyle(document.getElementById(id), 'width')) === 1989 ? fn() : setTimeout(poll, 100);
        })();
      },

      //载入依赖的js文件
      script: function (src, fn, jsname) {
        //未设置路径，则不主动加载js
        if (!timescale.path) return;
        if (typeof fn === 'string') jsname = fn;
        const app = (jsname || src).replace(/\.|\//g, '');
        let id = 'timescale-' + app,
          timeout = 0;

        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = timescale.path + src;
        script.id = id;

        if (!document.getElementById(id)) {
          document.body.appendChild(script);
        }

        if (typeof fn !== 'function') return;
        (function poll() {
          if (++timeout > (8 * 1000) / 100) {
            return window.console || console.error('konva.js: Invalid');
          }
          window.Konva ? fn() : setTimeout(poll, 100);
        })();
      },
    },
    timescale = {
      v: '1.0.0',
      config: {}, //全局配置项
      path: !window.Konva ? ready.getPath : '',

      //设置全局项
      set: function (options) {
        const that = this;
        that.config = $.extend({}, that.config, options);
        return that;
      },

      //主体css等待事件
      ready: function (fn) {
        if (!window.Konva) {
          const cssname = 'timescale',
            path = 'css/timescale.css?v=' + timescale.v;
          ready.link(path, cssname);
          // ready.link(path, function() {
          //引入依赖的konva.js文件
          ready.script('js/konva.min.js', fn, 'konva');
          // }, cssname);
        } else if (typeof fn === 'function') {
          fn();
        }
      },
    },
    //颜色值
    backgroundColor = '#004368',
    // backgroundColor = '#141f39',
    fontColor = '#fff',
    iconColor = '#097DB1',
    cursorColor = '#FF6600',
    //操作当前实例
    thisTimeScale = function () {
      const that = this;
      return {
        config: that.config,
        reload: that.reload.bind(that),
        seekTo: that.seekTo.bind(that),
        on: that.on.bind(that),
        off: that.off.bind(that),
        getClickTime: that.getClickTime.bind(that),
        setClickTime: that.setClickTime.bind(that),
      };
    },
    //组件构造器
    Class = function (options) {
      const that = this;
      that.config = $.extend({}, that.config, timescale.config, options);
      backgroundColor = that.config.backgroundColor || backgroundColor;
      fontColor = that.config.fontColor || fontColor;
      iconColor = that.config.iconColor || iconColor;
      cursorColor = that.config.cursorColor || cursorColor;

      //konva全局变量
      this.konva = {
        stage: null, // 舞台
        layer: null, //动态图层
        staticLayer: null, // 静态图层
        clipGroup: null, //剪辑片段分组
        indexGroup: null, //索引分组
        delClipEle: [], //将要删除的剪辑元素
        delIndexEle: [], //将要删除的索引元素
      };
      //事件队列(key为事件名，value为数组，数组内存放事件的回调函数)
      that.eventListObj = {};

      //变量初始化
      that.containerWidth = 0; //容器的宽度
      that.containerHeight = 0; //容器的高度
      that.mainTopBottomMargin = 20;
      that.aTotalTime = 60 * 60 * 1000; //默认总时间为一个小时
      that.currentTime = 0; // 当前时间
      that.clickTime = 0; // 跳转时间
      that.scale = 1;
      that.maxScale = false; //放大是否达到最大
      that.clippedArr = []; //剪掉的片段 [{startTime: 1000, endTime: 2000}]
      that.indexArr = []; //索引 [1000, 2000, 3000]
      //m_n 代表一屏
      that.m_nBeginTime = 0;
      that.m_nTotalTime = 0;
      // that.arrow = null;

      timescale.ready(function () {
        that.init();
      });
    },
    //DOM查找
    $ = function (selector) {
      return new JQ(selector);
    },
    //DOM构造器
    JQ = function (selector) {
      let index = 0,
        nativeDOM = typeof selector === 'object' ? [selector] : ((this.selector = selector), document.querySelectorAll(selector || null));
      for (; index < nativeDOM.length; index++) {
        this.push(nativeDOM[index]);
      }
    };

  //普通对象深度扩展
  $.extend = function () {
    var ai = 1,
      args = arguments,
      clone = function (target, obj) {
        target = target || (obj.constructor === Array ? [] : {});
        for (const i in obj) {
          //如果值为对象，则进入递归，继续深度合并
          target[i] = obj[i] && obj[i].constructor === Object ? clone(target[i], obj[i]) : obj[i];
        }
        return target;
      };

    args[0] = typeof args[0] === 'object' ? args[0] : {};

    for (; ai < args.length; ai++) {
      if (typeof args[ai] === 'object') {
        clone(args[0], args[ai]);
      }
    }
    return args[0];
  };
  //对象遍历
  $.each = function (obj, fn) {
    let key,
      that = this;
    if (typeof fn !== 'function') return that;
    obj = obj || [];
    if (obj.constructor === Object) {
      for (key in obj) {
        if (fn.call(obj[key], key, obj[key])) break;
      }
    } else {
      for (key = 0; key < obj.length; key++) {
        if (fn.call(obj[key], key, obj[key])) break;
      }
    }
    return that;
  };

  //数字前置补零
  $.digit = function (num, length) {
    let str = '';
    num = String(num);
    length = length || 2;
    for (let i = num.length; i < length; i++) {
      str += '0';
    }
    return num < Math.pow(10, length) ? str + (num | 0) : num;
  };

  //函数节流
  $.throttle = function (fun, delay) {
    let last, deferTimer;
    return function () {
      const that = this;
      const _args = arguments;
      const now = +new Date();
      //取当前时间和之前记录的时间对比，如果超过了规定的间隔则立即执行
      if (last && now < last + delay) {
        clearTimeout(deferTimer);
        deferTimer = setTimeout(function () {
          fun.apply(that, _args);
        }, delay);
      } else {
        last = now;
        fun.apply(that, _args);
      }
    };
  };

  //时间转换（毫秒秒转时分秒）
  $.msToHMS = function (time) {
    var hour,
      min,
      sec,
      time = time / 1000;
    hour = parseInt(time / (60 * 60));
    min = parseInt((time % (60 * 60)) / 60);
    sec = parseInt(time % 60);
    return $.digit(hour, 2) + ':' + $.digit(min, 2) + ':' + $.digit(sec, 2);
  };

  JQ.prototype = [];
  JQ.prototype.constructor = JQ;

  //追加字符
  JQ.addStr = function (str, new_str) {
    str = str.replace(/\s+/, ' ');
    new_str = new_str.replace(/\s+/, ' ').split(' ');
    $.each(new_str, function (ii, item) {
      if (!new RegExp('\\b' + item + '\\b').test(str)) {
        str = str + ' ' + item;
      }
    });
    return str.replace(/^\s|\s$/, '');
  };

  //移除值
  JQ.removeStr = function (str, new_str) {
    str = str.replace(/\s+/, ' ');
    new_str = new_str.replace(/\s+/, ' ').split(' ');
    $.each(new_str, function (ii, item) {
      const exp = new RegExp('\\b' + item + '\\b');
      if (exp.test(str)) {
        str = str.replace(exp, '');
      }
    });
    return str.replace(/\s+/, ' ').replace(/^\s|\s$/, '');
  };

  //查找子元素
  JQ.prototype.find = function (selector) {
    const that = this;
    let index = 0,
      arr = [],
      isObject = typeof selector === 'object';

    this.each(function (i, item) {
      const nativeDOM = isObject ? [selector] : item.querySelectorAll(selector || null);
      for (; index < nativeDOM.length; index++) {
        arr.push(nativeDOM[index]);
      }
      that.shift();
    });

    if (!isObject) {
      that.selector = (that.selector ? that.selector + ' ' : '') + selector;
    }

    $.each(arr, function (i, item) {
      that.push(item);
    });

    return that;
  };
  //DOM遍历
  JQ.prototype.each = function (fn) {
    return $.each.call(this, this, fn);
  };
  JQ.prototype.addClass = function (className, type) {
    return this.each(function (index, item) {
      item.className = JQ[type ? 'removeStr' : 'addStr'](item.className, className);
    });
  };

  //移除css类
  JQ.prototype.removeClass = function (className) {
    return this.addClass(className, true);
  };

  //是否包含css类
  JQ.prototype.hasClass = function (className) {
    let has = false;
    this.each(function (index, item) {
      if (new RegExp('\\b' + className + '\\b').test(item.className)) {
        has = true;
      }
    });
    return has;
  };

  JQ.prototype.css = function (attr, value) {
    //遍历选取当前元素
    for (let i = 0; i < this.length; i++) {
      this[i].style[attr] = value;
    }
    return this;
  };

  //添加或获取属性
  JQ.prototype.attr = function (key, value) {
    const that = this;
    return value === undefined
      ? (function () {
          if (that.length > 0) return that[0].getAttribute(key);
        })()
      : that.each(function (index, item) {
          item.setAttribute(key, value);
        });
  };

  //移除属性
  JQ.prototype.removeAttr = function (key) {
    return this.each(function (index, item) {
      item.removeAttribute(key);
    });
  };

  //设置HTML内容
  JQ.prototype.html = function (html) {
    return this.each(function (index, item) {
      item.innerHTML = html;
    });
  };

  //设置值
  JQ.prototype.val = function (value) {
    return this.each(function (index, item) {
      item.value = value;
    });
  };

  //追加内容
  JQ.prototype.append = function (elem) {
    return this.each(function (index, item) {
      typeof elem === 'object' ? item.appendChild(elem) : (item.innerHTML = item.innerHTML + elem);
    });
  };

  //移除内容
  JQ.prototype.remove = function (elem) {
    return this.each(function (index, item) {
      elem ? item.removeChild(elem) : item.parentNode.removeChild(item);
    });
  };

  //事件绑定
  JQ.prototype.on = function (eventName, fn) {
    return this.each(function (index, item) {
      item.attachEvent
        ? item.attachEvent('on' + eventName, function (e) {
            e.target = e.srcElement;
            fn.call(item, e);
          })
        : item.addEventListener(eventName, fn, false);
    });
  };

  //解除事件
  JQ.prototype.off = function (eventName, fn) {
    return this.each(function (index, item) {
      item.detachEvent ? item.detachEvent('on' + eventName, fn) : item.removeEventListener(eventName, fn, false);
    });
  };
  //
  Class.prototype.getClickTime = function () {
    return this.clickTime;
  };
  Class.prototype.setClickTime = function (val) {
    this.clickTime = val;
  };
  //绘制刻度线
  Class.prototype.drawLineF = function (layer) {
    // var that = this;
    let m_nParts = 0; //分为几大格
    let fScrap = 0; //不足一个大格的像素宽度
    let m_nBigCellTimeSpan = 0; //每个大格的时长(毫秒)
    const maxParts = 14; //一屏时间最多分多少个大格
    const m_nTotalTime = this.m_nTotalTime; //一屏区域将要显示的时长(毫秒)
    const m_nBeginTime = this.m_nBeginTime; //一屏区域开始的时间(毫秒)
    let m_nBigCellWidth = 0; //每个大格的宽度

    if (m_nTotalTime > 60 * 60 * 1000) {
      //时长大于一个小时的情况，单位等级：10分-20分-40分-80分...
      m_nBigCellTimeSpan = 10 * 60 * 1000; //初始10分钟一个大格(毫秒)
      do {
        m_nParts = parseInt(m_nTotalTime / m_nBigCellTimeSpan);
        fScrap = (m_nTotalTime % m_nBigCellTimeSpan) * (this.containerWidth / m_nTotalTime);
        m_nBigCellTimeSpan *= 2;
      } while (m_nParts > maxParts);

      m_nBigCellTimeSpan /= 2; //最终使用的Timespan
    } else if (m_nTotalTime > maxParts * 10 * 1000) {
      //时长小于1小时大于maxParts * 10 秒的情况，单位等级：20秒-40秒-60秒(1分)-5分<此时大格数肯定不会大于17个>
      m_nBigCellTimeSpan = 20 * 1000; //初始20秒一个大格(毫秒)
      m_nParts = maxParts + 1;
      for (var i = 0; i < 3 && m_nParts > maxParts; i++) {
        m_nParts = parseInt(m_nTotalTime / m_nBigCellTimeSpan);
        fScrap = (m_nTotalTime % m_nBigCellTimeSpan) * (this.containerWidth / m_nTotalTime);
        m_nBigCellTimeSpan += 20 * 1000;
      }
      m_nBigCellTimeSpan -= 20 * 1000; //最终使用的Timespan
      if (m_nParts > maxParts) {
        //1分钟等级分格仍然太多，则使用5分钟等级
        m_nBigCellTimeSpan = 5 * 60 * 1000;
        m_nParts = parseInt(m_nTotalTime / m_nBigCellTimeSpan);
        fScrap = (m_nTotalTime % m_nBigCellTimeSpan) * (this.containerWidth / m_nTotalTime);
      }
    } else {
      //时长小于maxParts * 10秒的情况，单位等级固定使用10秒
      m_nBigCellTimeSpan = 10 * 1000; //10秒一个大格，每小格1秒
      m_nParts = parseInt(m_nTotalTime / m_nBigCellTimeSpan);
      fScrap = (m_nTotalTime % m_nBigCellTimeSpan) * (this.containerWidth / m_nTotalTime);
    }

    if (m_nParts == 1) {
      //判断是否达到最大级别
      this.maxScale = true;
    }

    //每个大格的宽度
    m_nBigCellWidth = (this.containerWidth - fScrap) / m_nParts;
    //从0时间点到m_nBeginTime时间点之间在大格中所占的宽度
    const mDistance = m_nBigCellWidth * ((m_nBeginTime % m_nBigCellTimeSpan) / m_nBigCellTimeSpan);
    //用于显示的每大格起始时间
    let m_nBigCellStartTime = parseInt(m_nBeginTime / m_nBigCellTimeSpan) * m_nBigCellTimeSpan;

    let m_nBigCellStart_x = 0; // 每个大格的起始 x 位置
    for (var i = 0; i <= m_nParts + (fScrap > 0 ? 1 : 0); i++) {
      m_nBigCellStart_x = m_nBigCellWidth * i - mDistance;
      this.drawBigCell(layer, m_nBigCellStart_x, m_nBigCellWidth, m_nBigCellStartTime);
      m_nBigCellStartTime += m_nBigCellTimeSpan;
    }
  };

  //绘制一个大格
  Class.prototype.drawBigCell = function (layer, start_x, width, start_time) {
    const that = this;
    //绘制时间
    const text = new Konva.Text({
      x: start_x + 2,
      y: 25,
      // y: that.containerHeight - that.mainTopBottomMargin - 14,
      text: $.msToHMS(start_time),
      fontSize: 14,
      fill: fontColor,
    });
    layer.add(text);

    //绘制刻度
    //大格左右的两条线
    let line = new Konva.Line({
      points: [start_x, 0, start_x, that.containerHeight - 20],
      stroke: iconColor,
      strokeWidth: 1,
    });
    layer.add(line);
    line = new Konva.Line({
      points: [start_x + width, 0, start_x + width, that.containerHeight - 20],
      stroke: iconColor,
      strokeWidth: 1,
    });
    layer.add(line);

    //大格中线
    line = new Konva.Line({
      points: [start_x + width / 2, 0, start_x + width / 2, 15],
      stroke: iconColor,
      strokeWidth: 1,
    });
    layer.add(line);

    //大格短线
    const smallCellWidth = (width / 10).toFixed(2);
    for (let i = 1; i <= 10; i++) {
      if (i === 5) {
        continue;
      }
      line = new Konva.Line({
        points: [start_x + smallCellWidth * i, 0, start_x + smallCellWidth * i, 10],
        stroke: iconColor,
        strokeWidth: 1,
      });
      layer.add(line);
    }
    // const ele = document.getElementById('timescale-main');
    // const acrossLineWidth = (that.containerWidth = parseFloat(ready.getStyle(ele, 'width')));

    // //中间的横线
    // const acrossLine = new Konva.Line({
    //   points: [0, 20, acrossLineWidth, 20],
    //   stroke: iconColor,
    //   strokeWidth: 2,
    // });
    // layer.add(acrossLine);
  };
  // 鼠标所对应的当前时间
  Class.prototype.drawNowTime = function (text_x, nowTime) {
    if (this.nowTimeTitle) {
      this.nowTimeTitle.setAttrs({x: text_x + 12, text: $.msToHMS(nowTime)});
    } else {
      this.nowTimeTitle = new Konva.Text({
        x: text_x + 12,
        y: 15,
        text: $.msToHMS(nowTime),
        fontSize: 14,
        fill: fontColor,
      });
      this.konva.layer.add(this.nowTimeTitle);
    }
    this.konva.layer.batchDraw();
  };

  //绘制游标
  Class.prototype.drawCursor = function (layer) {
    const currentTime = this.currentTime;
    if (currentTime > this.aTotalTime) {
      this.currentTime = this.aTotalTime;
      return;
    }
    // $('.timescale .current-time').html($.msToHMS(currentTime));
    if (currentTime >= this.m_nBeginTime && currentTime <= this.m_nBeginTime + this.m_nTotalTime) {
      //游标在可视区域
      const arrow_x = ((currentTime - this.m_nBeginTime) / this.m_nTotalTime) * this.containerWidth;
      if (this.arrow) {
        // console.log('🚀 ~ this.statustrue:', this.status);
        this.arrow.setAttr('x', arrow_x - 6);
        layer.add(this.arrow); // 缩放后仍然显示刻度
        layer.draw();
      } else {
        // console.log('🚀 ~ this.statusfalse:', this.status);
        // if (!this.status) return;
        const that = this;
        Konva.Image.fromURL('/images/timeTarget.png', function (node) {
          node.setAttrs({
            x: arrow_x - 6,
            y: 10,
            scaleX: 1,
            scaleY: 1,
          });
          that.arrow = node;

          that.arrow.on('mousedown', function (e) {
            // document.onmousemove = null;
            mouse_start_x = e.evt.clientX;
            function mousemove(event) {
              const clickTime = ((event.layerX / that.containerWidth) * that.m_nTotalTime + that.m_nBeginTime) / 1000;
              if (that.currentTime > Math.round(clickTime) * 1000) return;
              // 移动当前时间
              that.drawNowTime(event.layerX, clickTime * 1000);
              // 移动角标
              mouse_end_x = event.clientX;
              const timeDiff = Math.round(((mouse_end_x - mouse_start_x) / that.containerWidth) * that.m_nTotalTime);
              if (timeDiff < 0) return; // 禁止反向移动
              that.clickTime += timeDiff;
              if (that.clickTime < 0) that.clickTime = 0;
              that.currentTime = currentTime;
              that.drawClickCursor(that.konva.layer);
              that.konva.layer.batchDraw();
              mouse_start_x = mouse_end_x;
            }
            document.onmousemove = mousemove;
          });
          layer.add(that.arrow);
          layer.draw();
        });
      }
      const that = this;
    }
  };
  //绘制跳动后的游标
  Class.prototype.drawClickCursor = function (layer) {
    const clickTime = this.clickTime;
    if (clickTime > this.aTotalTime) {
      this.clickTime = this.aTotalTime;
      return;
    }
    $('.timescale .current-time').html($.msToHMS(clickTime));
    if (clickTime >= this.m_nBeginTime && clickTime <= this.m_nBeginTime + this.m_nTotalTime) {
      //游标在可视区域
      const arrow_x = ((clickTime - this.m_nBeginTime) / this.m_nTotalTime) * this.containerWidth;
      if (this.click_arrow) {
        this.click_arrow.setAttr('x', arrow_x - 6);
        layer.add(this.click_arrow); // 缩放后仍然显示刻度
        layer.draw();
        // this.emit('getClickTime');
      } else {
        const that = this;
        Konva.Image.fromURL('/images/timeTarget.png', function (node) {
          node.setAttrs({
            x: arrow_x - 6,
            y: 10,
            scaleX: 1,
            scaleY: 1,
          });
          that.click_arrow = node;
          that.click_arrow.on('mouseup', function (e) {
            console.log('🚀 ~ up:', e);
            // setTimeout(() => {
            //   that.seekTo(1, that.clickTime / 2);
            // }, 500);
            // setTimeout(() => {
            //   that.seekTo(1, that.clickTime);
            // }, 1500);
            // that.emit('getClickTime');
            document.onmousemove = null;
          });
          layer.add(that.click_arrow);
          layer.draw();
        });
      }
    }
  };
  //更改进度后背景颜色区分
  Class.prototype.drawBackground = function (layer) {
    const currentTime = this.currentTime;
    const rectWidch = ((currentTime - this.m_nBeginTime) / this.m_nTotalTime) * this.containerWidth;
    if (this.throughdRect) {
      this.throughdRect.setAttr('width', rectWidch);
      layer.add(this.throughdRect); // 缩放后仍然显示刻度
      layer.draw();
    } else {
      const that = this;
      //背景颜色
      const throughdRect = new Konva.Rect({
        x: 0,
        y: 5,
        width: rectWidch,
        height: 15,
        fill: '#274885',
        // fill: backgroundColor,
      });
      this.throughdRect = throughdRect;
      layer.add(this.throughdRect);
      layer.draw();
    }
    // }
  };
  Class.prototype.reset = function (time) {};
  //绝对时间转相对时间
  Class.prototype.absToRel = function (time) {
    const sectionArr = this.config.sectionArr;
    let section_start = 0,
      section_end;
    for (let i = 0, len = sectionArr.length; i < len; i++) {
      section_end = section_start + sectionArr[i].duration;
      if (time >= section_start && time <= section_end) {
        return {
          id: sectionArr[i].id,
          time: time - section_start,
          absTime: time,
        };
      }
      section_start = section_end;
    }
  };

  //相对时间转绝对时间
  Class.prototype.relToAbs = function (id, time) {
    const sectionArr = this.config.sectionArr;
    let section_start = 0;
    for (let i = 0, len = sectionArr.length; i < len; i++) {
      if (id == sectionArr[i].id) {
        return section_start + time;
      }
      section_start += sectionArr[i].duration;
    }
  };

  //插件内部事件初始化
  Class.prototype.eventInit = function () {
    const that = this;
    //放大按钮
    $('#timescale-zoomIn').on('click', function (e) {
      if (that.maxScale) {
        return;
      }
      if (that.scale === 128) {
        return;
      }
      that.scale *= 2;
      that.m_nTotalTime = that.aTotalTime / that.scale;
      //将游标尽可能的居中
      if (that.currentTime >= that.m_nTotalTime / 2 && that.aTotalTime - that.currentTime >= that.m_nTotalTime / 2) {
        //如果条件允许，则将游标置于最中间
        that.m_nBeginTime = that.currentTime - that.m_nTotalTime / 2;
      } else if (that.currentTime < that.m_nTotalTime / 2) {
        that.m_nBeginTime = 0;
      } else if (that.aTotalTime - that.currentTime < that.m_nTotalTime / 2) {
        that.m_nBeginTime = that.aTotalTime - that.m_nTotalTime;
      }

      //重置刻度层
      that.konva.layer.removeChildren();
      that.drawLineF(that.konva.layer);
      that.drawCursor(that.konva.layer);
      that.konva.layer.draw();

      //改变滚动条的宽度和位置
      const startPosition = (that.m_nBeginTime / that.aTotalTime) * 100;
      const width = (that.m_nTotalTime / that.aTotalTime) * 100;
      $('#timescale-scroll-bar').attr('style', 'width:' + width + '%;left:' + startPosition + '%');
    });

    //缩小按钮
    $('#timescale-zoomOut').on('click', function (e) {
      that.scale /= 2;
      if (that.scale < 1) {
        that.scale = 1;
        return;
      }
      that.maxScale = false;
      that.m_nTotalTime = that.aTotalTime / that.scale;

      //将游标尽可能的居中
      if (that.currentTime >= that.m_nTotalTime / 2 && that.aTotalTime - that.currentTime >= that.m_nTotalTime / 2) {
        //如果条件允许，则将游标置于最中间
        that.m_nBeginTime = that.currentTime - that.m_nTotalTime / 2;
      } else if (that.currentTime < that.m_nTotalTime / 2) {
        that.m_nBeginTime = 0;
      } else if (that.aTotalTime - that.currentTime < that.m_nTotalTime / 2) {
        that.m_nBeginTime = that.aTotalTime - that.m_nTotalTime;
      }
      //重置刻度层
      that.konva.layer.removeChildren();
      that.drawLineF(that.konva.layer);
      that.drawCursor(that.konva.layer);
      that.konva.layer.draw();

      //改变滚动条的宽度和位置
      const startPosition = (that.m_nBeginTime / that.aTotalTime) * 100;
      const width = (that.m_nTotalTime / that.aTotalTime) * 100;
      $('#timescale-scroll-bar').attr('style', 'width:' + width + '%;left:' + startPosition + '%');
    });

    //滚动条拖拽事件
    let old_x = 0;
    let mouse_start_x = 0;
    function mousemove(e) {
      let scroll_left = ((e.clientX - mouse_start_x + old_x) / that.containerWidth) * 100;
      const self_width = parseFloat(ready.getStyle($('#timescale-scroll-bar')[0], 'width'));
      //边界碰撞检测
      if (scroll_left <= 0) {
        scroll_left = 0;
      } else if (scroll_left >= 100 - (self_width / that.containerWidth) * 100) {
        scroll_left = 100 - (self_width / that.containerWidth) * 100;
      }
      //调整时间轴的开始时间
      that.m_nBeginTime = (that.aTotalTime * scroll_left) / 100;
      that.konva.layer.removeChildren();
      that.drawLineF(that.konva.layer);
      that.drawCursor(that.konva.layer);
      that.konva.layer.batchDraw();
      $('#timescale-scroll-bar').css('left', scroll_left + '%');
    }
    $('#timescale-scroll-bar').on('mousedown', function (e) {
      mouse_start_x = e.clientX;
      old_x = parseFloat(ready.getStyle($('#timescale-scroll-bar')[0], 'left'));
      // $(document).on('mousemove', mousemove);
      // document.onmousemove = $.throttle(mousemove, 30);
      document.onmousemove = mousemove;
    });
    $(document).on('mouseup', function (e) {
      // setTimeout(() => {
      //   that.seekTo(1, that.clickTime / 2);
      // }, 500);
      // setTimeout(() => {
      //   that.seekTo(1, that.clickTime);
      // }, 1500);
      document.onmousemove = null;
    });

    //滚动条点击事件
    $('.timescale .timescale-scroll-mouse-zoom').on('click', function (e) {
      let start_x = e.clientX - e.currentTarget.getBoundingClientRect().left;
      const bar_w = parseFloat(ready.getStyle($('#timescale-scroll-bar')[0], 'width'));
      if (start_x >= that.containerWidth - bar_w) {
        start_x = that.containerWidth - bar_w;
      }
      that.m_nBeginTime = (start_x / that.containerWidth) * that.aTotalTime;
      that.konva.layer.removeChildren();
      that.drawLineF(that.konva.layer);
      that.drawCursor(that.konva.layer);
      that.konva.layer.batchDraw();
      $('#timescale-scroll-bar').css('left', (start_x / that.containerWidth) * 100 + '%');
    });
  };

  //找出剪辑片段中包含的视频片段
  //返回一个包含具体视频片段的数组
  Class.prototype.findVideoSection = function (arr) {
    const that = this;
    const resultArr = [],
      clipsArr = arr,
      sectionArr = JSON.parse(JSON.stringify(that.config.sectionArr));
    const clips_len = clipsArr.length,
      section_len = sectionArr.length;
    for (let i = 0; i < clips_len; i++) {
      for (let j = 0; j < section_len; j++) {
        if (j === 0) {
          sectionArr[j].startTime = 0;
          sectionArr[j].endTime = sectionArr[j].duration;
        } else {
          sectionArr[j].startTime = sectionArr[j - 1].endTime;
          sectionArr[j].endTime = sectionArr[j].startTime + sectionArr[j].duration;
        }
        if (clipsArr[i].startTime >= sectionArr[j].startTime && clipsArr[i].endTime <= sectionArr[j].endTime) {
          //片段位于当前视频上
          resultArr.push({
            section: [
              {
                id: sectionArr[j].id,
                startTime: clipsArr[i].startTime - sectionArr[j].startTime,
                endTime: clipsArr[i].endTime - sectionArr[j].startTime,
              },
            ],
            allStartTime: clipsArr[i].startTime,
            allEndTime: clipsArr[i].endTime,
          });
          break;
        } else if (clipsArr[i].startTime >= sectionArr[j].startTime && clipsArr[i].endTime > sectionArr[j].endTime && sectionArr[j].endTime > clipsArr[i].startTime) {
          //片段的前一部分在当前视频上
          resultArr.push({
            section: [
              {
                id: sectionArr[j].id,
                startTime: clipsArr[i].startTime - sectionArr[j].startTime,
                endTime: sectionArr[j].duration,
              },
            ],
            allStartTime: clipsArr[i].startTime,
            allEndTime: clipsArr[i].endTime,
          });
        } else if (clipsArr[i].startTime < sectionArr[j].startTime && clipsArr[i].endTime <= sectionArr[j].endTime) {
          //片段的后一部分在当前视频上
          resultArr[resultArr.length - 1].section.push({
            id: sectionArr[j].id,
            startTime: 0,
            endTime: clipsArr[i].endTime - sectionArr[j].startTime,
          });
          break;
        } else if (clipsArr[i].startTime < sectionArr[j].startTime && clipsArr[i].endTime > sectionArr[j].endTime) {
          //片段的中间一部分在当前视频上
          resultArr[resultArr.length - 1].section.push({
            id: sectionArr[j].id,
            startTime: 0,
            endTime: sectionArr[j].duration,
          });
        }
      }
    }
    return resultArr;
  };

  //剪辑片段反选
  Class.prototype.clippedReverse = function (clippedArr) {
    const clipped_len = clippedArr.length;
    const clipsArr = [];
    for (let i = 0; i < clipped_len; i++) {
      if (i === 0) {
        if (clippedArr[i].startTime !== 0) {
          //剪辑片段位于最开头
          clipsArr.push({
            startTime: 0,
            endTime: clippedArr[i].startTime,
          });
        }
      }
      if (i === clipped_len - 1) {
        //剪辑片段位于最末尾
        if (clippedArr[i].endTime !== this.aTotalTime) {
          clipsArr.push({
            startTime: clippedArr[i].endTime,
            endTime: this.aTotalTime,
          });
        }
      }
      if (i < clipped_len - 1) {
        clipsArr.push({
          startTime: clippedArr[i].endTime,
          endTime: clippedArr[i + 1].startTime,
        });
      }
    }
    return clipsArr;
  };

  //触发对应的事件
  Class.prototype.emit = function (event) {
    const argu = [];
    for (var i = 1, len = arguments.length; i < len; i++) {
      argu.push(arguments[i]);
    }
    if (this.eventListObj.hasOwnProperty(event)) {
      for (var i = 0, len = this.eventListObj[event].length; i < len; i++) {
        this.eventListObj[event][i].apply(null, argu);
      }
    }
  };

  //等待插件初始化完毕
  Class.prototype.wait = function (fun) {
    const that = this;
    (function poll() {
      that.konva.layer ? fun() : setTimeout(poll, 100);
    })();
  };
  /******** 插件暴露在外的方法 start ***********/
  //重新加载时间轴
  Class.prototype.reload = function (sectionArr, clipsArr) {
    const that = this;
    this.wait(function () {
      that.currentTime = 0;
      that.m_nBeginTime = 0;
      that.clippedArr = [];
      that.indexArr = [];
      that.konva.layer.removeChildren();
      that.konva.staticLayer.removeChildren();
      that.konva.delClipEle = [];
      that.konva.delIndexEle = [];
      that.maxScale = false;
      that.scale = 1;
      that.config.sectionArr = sectionArr;
      $('#timescale-scroll-bar').css('width', '100%').css('left', '0');
      that.frameInit();
      that.timelineLoad(sectionArr, clipsArr);
    });
  };
  //设置进度
  Class.prototype.seekTo = function (id, time) {
    const that = this;
    this.wait(function () {
      let total = 0;
      const sectionArr = that.config.sectionArr;
      for (let i = 0, len = sectionArr.length; i < len; i++) {
        if (id == sectionArr[i].id) {
          total += time;
          break;
        } else {
          total += sectionArr[i].duration;
        }
      }
      that.currentTime = total;
      if (that.currentTime >= that.clickTime && that.click_arrow) {
        that.click_arrow.remove();
      }
      that.drawCursor(that.konva.layer);
      that.drawBackground(that.konva.staticLayer);
      that.konva.layer.batchDraw();
    });
  };
  // //设置进度
  // Class.prototype.seekTo = $.throttle(function (id, time) {
  //   const that = this;
  //   this.wait(function () {
  //     let total = 0;
  //     const sectionArr = that.config.sectionArr;
  //     for (let i = 0, len = sectionArr.length; i < len; i++) {
  //       if (id == sectionArr[i].id) {
  //         total += time;
  //         break;
  //       } else {
  //         total += sectionArr[i].duration;
  //       }
  //     }
  //     that.currentTime = total;
  //     if (that.currentTime >= that.clickTime && that.click_arrow) {
  //       that.click_arrow.remove();
  //     }
  //     that.drawCursor(that.konva.layer);
  //     that.drawBackground(that.konva.staticLayer);
  //     that.konva.layer.batchDraw();
  //   });
  // }, 500);

  //监听插件的事件
  Class.prototype.on = function (event, fun) {
    if (this.eventListObj.hasOwnProperty(event)) {
      this.eventListObj[event].push(fun);
    } else {
      this.eventListObj[event] = [fun];
    }
  };

  //移除插件的事件监听
  Class.prototype.off = function (event, fun) {
    if (this.eventListObj.hasOwnProperty(event)) {
      for (let i = 0, len = this.eventListObj[event].length; i < len; i++) {
        if (this.eventListObj[event][i] === fun) {
          this.eventListObj[event].splice(i, 1);
        }
      }
    }
  };

  /******** 插件暴露在外的方法 end ***********/

  Class.prototype.init = function () {
    const that = this;
    that.frameInit();
    that.timelineLoad(that.config.sectionArr, that.config.clipsArr);
    that.eventInit();
  };

  /**
   * @method timelineLoad
   * @desc 用来初始化视频剪辑控件
   * @param {string} sectionArr - [{id: 1, duration: 100000}]
   * 备注：此参数为所有视频片段的描述信息，id为视频片段的唯一标识，duration为每个片段的时长(毫秒)
   * @param {string} clipsArr - [{startTime: 0, endTime: 2000}, {startTime: 4000, endTime: 100000}]
   * 备注：单位为毫秒ms已保存的片段描述信息，startTime为拼合后起始点的时长，endTime为拼合后终止点的时长
   * @returns void
   */
  Class.prototype.timelineLoad = function (sectionArr, clipsArr) {
    const that = this;
    const ele = document.getElementById('timescale-main');
    const width = (that.containerWidth = parseFloat(ready.getStyle(ele, 'width')));
    const height = (that.containerHeight = parseFloat(ready.getStyle(ele, 'height')));
    const canvasTags = ele.getElementsByTagName('canvas');
    for (let index = 0; index < canvasTags.length; index++) {
      canvasTags[index].width = width;
      canvasTags[index].style.width = `${width}px`;
    }
    let totalTime = 0;

    // //计算总时间
    for (let i = 0, len = sectionArr.length; i < len; i++) {
      totalTime += sectionArr[i].duration;
    }
    that.status = sectionArr[0].status;
    that.aTotalTime = totalTime;
    $('.timescale .total-time').html($.msToHMS(totalTime));

    //背景颜色
    const rect = new Konva.Rect({
      x: 0,
      y: 0,
      width: width,
      height: height,
      fill: backgroundColor,
    });
    // add the shape to the layer
    that.konva.staticLayer.add(rect);
    that.konva.staticLayer.draw();
    //中间的横线
    const line = new Konva.Line({
      points: [0, 0, width, 0],
      stroke: iconColor,
      strokeWidth: 2,
    });
    that.konva.layer.add(line);
    that.m_nTotalTime = that.aTotalTime;
    that.drawLineF(that.konva.layer);
    that.drawCursor(that.konva.layer);
    that.konva.layer.draw();
  };

  /**
   * @method frameInit
   * @desc 时间轴框架初始化
   */
  Class.prototype.frameInit = function () {
    const that = this;
    const ele = document.getElementById(that.config.ele);
    const operation =
      "<div class='timescale-operation'>\
      <div class='timescale-timeshow'>\
        <span>当前：<span class='current-time'>00:00:00</span></span>\
        <span>总时长：<span class='total-time'>00:00:00</span></span>\
      </div>" +
      "<div class='timescale-operation-group'>\
        <span title='放大' id='timescale-zoomIn'><i class='iconfont icon-timescale-fangda'></i></span>\
        <span title='缩小' id='timescale-zoomOut'><i class='iconfont icon-timescale-suoxiao'></i></span>\
      </div>\
    </div>";
    const main = "<div id='timescale-main'></div>";

    const totalSrc = operation + main;

    if ($('#timescale-css').length == 0) {
      //自定义颜色导入
      const style = document.createElement('style');
      style.type = 'text/css';
      style.id = 'timescale-css';
      style.innerHTML =
        '.timescale{border-color:' +
        iconColor +
        '}.timescale-operation{background-color:' +
        backgroundColor +
        '}.timescale-operation .iconfont{color:' +
        iconColor +
        '}.timescale-operation .timescale-timeshow>span{color:' +
        fontColor +
        '}.timescale-operation .timescale-timeshow .current-time,.timescale-operation .timescale-timeshow .total-time{border-color:' +
        iconColor +
        ';-webkit-box-shadow: 0 0 5px 1px ' +
        iconColor +
        ' inset;-moz-box-shadow: 0 0 5px 1px ' +
        iconColor +
        ' inset;box-shadow: 0 0 5px 1px ' +
        iconColor +
        ' inset;}.timescale-operation .timescale-operation-group{border-color:' +
        iconColor +
        '}#timescale-scroll-bar{background-color:' +
        iconColor +
        '}#timescale-main{border-color:' +
        iconColor +
        '}.timescale-operation .timescale-operation-group span:hover{-webkit-box-shadow: 0 0 10px 1px ' +
        iconColor +
        ' inset;-moz-box-shadow: 0 0 10px 1px ' +
        iconColor +
        ' inset;box-shadow: 0 0 10px 1px ' +
        iconColor +
        ' inset;}';
      $(document.head).append(style);
    }

    $(ele).addClass('timescale');
    $(ele).html(totalSrc);

    //初始化画布
    const canvas = document.getElementById('timescale-main');
    const width = (that.containerWidth = parseFloat(ready.getStyle(canvas, 'width')));
    const height = (that.containerHeight = parseFloat(ready.getStyle(canvas, 'height')));
    that.konva.stage = new Konva.Stage({
      container: 'timescale-main',
      width: width,
      height: height,
    });
    //动态变化的图层
    that.konva.layer = new Konva.Layer();
    //初始化不再改变的图层
    that.konva.staticLayer = new Konva.Layer();
    that.konva.staticLayer.on('click', function (e) {
      const clickTime = ((e.evt.layerX / that.containerWidth) * that.m_nTotalTime + that.m_nBeginTime) / 1000;
      // if (that.currentTime > Math.round(clickTime) * 1000) return;

      that.clickTime = Math.round(clickTime) * 1000;
      // if (that.currentTime > that.clickTime) return;

      that.drawClickCursor(that.konva.layer);
      that.konva.layer.draw();
      that.emit('getClickTime');
      // that.emit('seekTo', that.absToRel(that.currentTime));
    });
    function nowTimeMousemove(e) {
      const clickTime = ((e.layerX / that.containerWidth) * that.m_nTotalTime + that.m_nBeginTime) / 1000;
      // if (that.currentTime > Math.round(clickTime) * 1000) return;
      that.drawNowTime(e.layerX, clickTime * 1000);
    }
    that.konva.staticLayer.on('mouseenter', function (e) {
      if (that.nowTimeTitle) {
        that.nowTimeTitle.show();
        that.konva.layer.draw();
      }
      mouse_start_x = e.clientX;
      document.onmousemove = nowTimeMousemove;
    });
    $('.konvajs-content').on('mouseout', function (e) {
      if (that.nowTimeTitle) {
        that.nowTimeTitle.hide();
        that.konva.layer.draw();
      }
      // document.onmousemove = null;
    });

    //初始化剪辑片段组
    this.konva.clipGroup = new Konva.Group();
    //初始化索引组
    this.konva.indexGroup = new Konva.Group();
    that.konva.stage.add(that.konva.staticLayer, that.konva.layer);
  };

  timescale.render = function (options) {
    const inst = new Class(options);
    return thisTimeScale.call(inst);
  };

  window.timescale = timescale;
  timescale.ready();
})();

